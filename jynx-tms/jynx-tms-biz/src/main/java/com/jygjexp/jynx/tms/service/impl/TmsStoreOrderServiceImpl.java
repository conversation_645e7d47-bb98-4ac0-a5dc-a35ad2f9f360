package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.security.util.SecurityUtils;
import com.jygjexp.jynx.tms.constants.StoreConstants;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.dto.*;
import com.jygjexp.jynx.tms.entity.*;
import com.jygjexp.jynx.tms.exception.CustomBusinessException;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.utils.*;
import com.jygjexp.jynx.tms.vo.*;
import com.jygjexp.jynx.tms.vo.api.*;
import com.jygjexp.jynx.zxoms.excel.ExcelReader;
import com.jygjexp.jynx.zxoms.excel.exception.ExcelValidatedException;
import lombok.RequiredArgsConstructor;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 快递客户订单
 *
 * <AUTHOR>
 * @date 2025-07-14 17:34:55
 */
@Service
@RequiredArgsConstructor
public class TmsStoreOrderServiceImpl extends MPJBaseServiceImpl<TmsStoreOrderMapper, TmsStoreOrderEntity> implements TmsStoreOrderService {
    private static final Logger logger = LoggerFactory.getLogger(TmsStoreOrderServiceImpl.class);
    private final TmsStoreOrderGoodsService storeOrderGoodsService;
    private final TmsStoreCustomerService storeCustomerService;
    private final TmsStoreProviderRelationService storeProviderRelationService;
    private final TmsServiceProviderService serviceProviderService;
    private final TmsStoreService storeService;
    private final TmsRoutePlanService routePlanService;
    private final Validator validator;
    private final TmsStoreUserService storeUserService;
    private final TmsStoreOrderExceptionService tmsStoreOrderExceptionService;
    private final TmsStoreOrderPkgPayService tmsStoreOrderPkgPayService;
    private final TmsCustomerService customerService;
    private final TmsCustomerOrderService customerOrderService;
    private final TmsReceivableService tmsReceivableService;
    private final TmsStoreOrderTraceService tmsStoreOrderTraceService;
    private final TmsStoreBalanceService storeBalanceService;
    private final TmsBlindBoxService blindBoxService;
    private final TmsBlindBoxRuleService blindBoxRuleService;
    private final TmsStoreMessageTraceService storeMessageTraceService;
    private final PostBusinessUtils postBusinessUtils;
    private final ObjectMapper mapper = new ObjectMapper();

    @Override
    public Page<StoreOrderVO> selectPage(Page page, StoreOrderQueryDTO storeOrderQueryDTO) {
        Long customerId = storeOrderQueryDTO.getStoreCustomerId();
        if(null == customerId){
            Long userId = SecurityUtils.getUser().getId();
            Long storeCustomerId = storeCustomerService.getStoreCustomerIdByUserId(userId);
            storeOrderQueryDTO.setStoreCustomerId(storeCustomerId);
        }
        // 使用 XML 中的连表查询，直接返回分页结果
        IPage<StoreOrderVO> result = baseMapper.selectPageWithJoin(page, storeOrderQueryDTO);
        if(null == result){
            return new Page<>();
        }
        // 根据单位制转换
        result.getRecords().forEach(e->{
            Integer unitType = e.getUnitType();
            unitType = unitType == null ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
            // 法英制度 单位换算
            if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
                BigDecimal totalWeight = e.getTotalWeight();
                if(null != totalWeight){
                    totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
                    e.setTotalWeight(totalWeight);
                }
                BigDecimal totalVolume = e.getTotalVolume();
                if(null != totalVolume){
                    totalVolume = UnitConvertUtil.cubicInchesToCubicMeters(totalVolume);
                    e.setTotalVolume(totalVolume);
                }
            }else{
                BigDecimal totalVolume = e.getTotalVolume();
                if(null != totalVolume){
                    e.setTotalVolume(UnitConvertUtil.volumeCM3ConvertToM3(totalVolume));
                }
            }
        });
        return (Page<StoreOrderVO>) result;
    }

    @Override
    public List<StoreOrderExcelVO> exportStoreOrder(Page page, StoreOrderQueryDTO storeOrderQueryDTO) {
        Page<StoreOrderVO> storeOrderVOPage = this.selectPage(page, storeOrderQueryDTO);
        if(null == storeOrderVOPage){
            return new ArrayList<>();
        }
        List<StoreOrderExcelVO> storeOrderExcelVOS = new ArrayList<>();
        for (StoreOrderVO record : storeOrderVOPage.getRecords()) {
            StoreOrderExcelVO exportBean = BeanUtil.toBean(record, StoreOrderExcelVO.class);
            storeOrderExcelVOS.add(exportBean);
        }
        return storeOrderExcelVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean saveStoreOrder(StoreOrderDTO storeOrderDTO) {
        Long storeCustomerId = storeOrderDTO.getStoreCustomerId();
        TmsStoreCustomerEntity storeCustomer = null;
        if(null == storeCustomerId){
            Long userId = SecurityUtils.getUser().getId();
            storeCustomer = storeCustomerService.getStoreCustomerByUserId(userId);
        }else{
            storeCustomer = storeCustomerService.getStoreCustomerById(storeCustomerId);
        }
        if(null == storeCustomer){
            throw new CustomBusinessException("客户不存在,不支持下单");
        }
        storeOrderDTO.setStoreCustomerId(storeCustomer.getId());
        // 存在门店则设置
        storeOrderDTO.setStoreId(storeCustomer.getStoreId());

        List<StoreOrderGoodsDTO> storeOrderGoodsDTOS = storeOrderDTO.getStoreOrderGoods();
        if(CollUtil.isEmpty(storeOrderGoodsDTOS)){
            throw new CustomBusinessException("货物信息为空，不支持下单");
        }
        // 校验重量
        verifyGoodsOverWeight(storeOrderGoodsDTOS,storeOrderDTO.getUnitType());
        StoreProviderRelationVO providerRelation = storeOrderDTO.getStoreProviderRelationVO();
        if(null == providerRelation){
            throw new CustomBusinessException("请选择对应末端派送服务商");
        }
        BigDecimal freightAmount = providerRelation.getFreightAmount();
        if(null == freightAmount || freightAmount.compareTo(BigDecimal.ZERO) <= 0){
            throw new CustomBusinessException("运费异常,不支持下单");
        }
        storeOrderDTO.setTotalFreightAmount(freightAmount);

        TmsStoreBalanceEntity storeCustomerBalance = storeBalanceService.getByStoreCustomerId(storeOrderDTO.getStoreCustomerId());
        if(null == storeCustomerBalance){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不存在,不支持下单");
        }
        // 查询主单未核销订单金额-当前客户 -产品提出移除 0807
//        BigDecimal totalFreightAmountSum = baseMapper.getTotalFreightAmountSum(storeOrderDTO.getStoreCustomerId());
//        BigDecimal amount = storeCustomerBalance.getAmount();
//        BigDecimal checkAmount = freightAmount.add(totalFreightAmountSum);
//        if(checkAmount.compareTo(amount) > 0){
//            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不足,请前往充值");
//        }
        Long orderCount = baseMapper.getSubOrderCount();
        int orderCounts = orderCount != null ? orderCount.intValue() : 0;
        String mainOrderNo = ExpressOrderNoUtil.generateMainOrderNo(orderCounts);
        TmsStoreOrderEntity mainStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
        mainStoreOrder.setEntrustedOrderNumber(mainOrderNo);
        mainStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.MAIN.getValue());
        List<TmsStoreOrderEntity> saveStoreOrders = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<String> businessKeys = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);

        AtomicInteger index = new AtomicInteger(0);
        saveStoreOrders.add(mainStoreOrder);
        for (StoreOrderGoodsDTO storeOrderGoodsDTO : storeOrderGoodsDTOS) {
            TmsStoreOrderEntity subStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
            String subOrderNo = ExpressOrderNoUtil.generateSubOrderNo(mainOrderNo, index.incrementAndGet());
            subStoreOrder.setEntrustedOrderNumber(subOrderNo);
            subStoreOrder.setMainEntrustedOrder(mainOrderNo);
            subStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.SUB.getValue());
            saveStoreOrders.add(subStoreOrder);
            businessKeys.add(subOrderNo);

            TmsStoreOrderGoodsEntity storeOrderGood = BeanUtil.toBean(storeOrderGoodsDTO,TmsStoreOrderGoodsEntity.class);
            storeOrderGood.setUnitType(storeOrderDTO.getUnitType());
            storeOrderGood.setSubEntrustedOrder(subOrderNo);
            storeOrderGood.setMainEntrustedOrder(mainOrderNo);
            storeOrderGoods.add(storeOrderGood);

            TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
            traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue());
            traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getEName());
            traceEntity.setSubEntrustedOrder(subOrderNo);
            traceEntity.setMainEntrustedOrder(mainOrderNo);
            storeOrderTraces.add(traceEntity);
        }
        if(CollUtil.isNotEmpty(saveStoreOrders)){
            baseMapper.insert(saveStoreOrders);
        }
        if(CollUtil.isNotEmpty(businessKeys)){
            // 站内消息推送
            storeMessageTraceService.saveStoreMessageTrace(storeOrderDTO.getStoreCustomerId(),StoreEnums.StoreMessageTrace.BusinessSubType.ORDER_SUCCESS.getValue(),businessKeys);
        }
        if(CollUtil.isNotEmpty(storeOrderGoods)){
            storeOrderGoodsService.saveStoreOrderGoods(storeOrderGoods);
        }
        if(CollUtil.isNotEmpty(storeOrderTraces)){
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }
        StoreProviderRelationVO storeProviderRelationVO = storeOrderDTO.getStoreProviderRelationVO();
        TmsStoreProviderRelationEntity saveBean = BeanUtil.toBean(storeProviderRelationVO, TmsStoreProviderRelationEntity.class);
        saveBean.setMainEntrustedOrder(mainOrderNo);
        storeProviderRelationService.saveStoreOrderProvider(saveBean);
        return true;
    }

    private void verifyGoodsOverWeight(List<StoreOrderGoodsDTO> storeOrderGoodsDTOS,Integer unitType){
        if(CollUtil.isNotEmpty(storeOrderGoodsDTOS) && null != unitType){
            if(UnitConvertUtil.UNIT_INTERNATIONAL.equals(unitType)){
                boolean overWeight = storeOrderGoodsDTOS
                        .stream()
                        .anyMatch(e-> StoreConstants.WEIGHT_LIMIT_KG.compareTo(e.getWeight()) < 0);
                if(overWeight){
                    throw new CustomBusinessException("重量不能超过68kg");
                }
            }else if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
                boolean overWeight = storeOrderGoodsDTOS
                        .stream()
                        .anyMatch(e-> StoreConstants.WEIGHT_LIMIT_LB.compareTo(e.getWeight()) < 0);
                if(overWeight){
                    throw new CustomBusinessException("重量不能超过150磅");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreOrderById(StoreOrderDTO storeOrderDTO) {
        Long id = storeOrderDTO.getId();
        if(null == id){
            throw new CustomBusinessException("订单id不能为空");
        }
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            throw new CustomBusinessException("客户订单不存在");
        }
        if(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue().equals(storeOrder.getWriteOffFlag())){
            throw new CustomBusinessException("订单已核销,不支持修改");
        }

        // 货物信息
        List<StoreOrderGoodsDTO> storeOrderGoodsDTOS = storeOrderDTO.getStoreOrderGoods();
        if(CollUtil.isEmpty(storeOrderGoodsDTOS)){
            // 预留全部删除的逻辑
            throw new CustomBusinessException("订单信息不能为空,不支持操作");
        }
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        TmsStoreOrderEntity mainOrder = new TmsStoreOrderEntity();
        BeanUtil.copyProperties(storeOrderDTO,mainOrder);
        mainOrder.setId(storeOrder.getId());
        mainOrder.setStoreCustomerId(storeOrder.getStoreCustomerId());
        mainOrder.setMainEntrustedOrder(entrustedOrderNumber);
        baseMapper.updateById(mainOrder);
        // 删除所有子单
        baseMapper.deleteByMainEntrustedNo(entrustedOrderNumber);
        // 删除所有子单 删除所有子单货物信息
        storeOrderGoodsService.deleteByMainEntrustedOrder(entrustedOrderNumber);
        List<TmsStoreOrderEntity> saveStoreOrders = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = new ArrayList<>(storeOrderGoodsDTOS.size() + 1);
        AtomicInteger index = new AtomicInteger(0);
        for (StoreOrderGoodsDTO storeOrderGoodsDTO : storeOrderGoodsDTOS) {
            TmsStoreOrderEntity subStoreOrder = BeanUtil.copyProperties(storeOrderDTO, TmsStoreOrderEntity.class,"id");
            String subOrderNo = ExpressOrderNoUtil.generateSubOrderNo(entrustedOrderNumber, index.incrementAndGet());
            subStoreOrder.setEntrustedOrderNumber(subOrderNo);
            subStoreOrder.setStoreCustomerId(storeOrder.getStoreCustomerId());
            subStoreOrder.setMainEntrustedOrder(entrustedOrderNumber);
            subStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.SUB.getValue());
            saveStoreOrders.add(subStoreOrder);

            TmsStoreOrderGoodsEntity storeOrderGood = BeanUtil.toBean(storeOrderGoodsDTO,TmsStoreOrderGoodsEntity.class);
            storeOrderGood.setUnitType(storeOrderDTO.getUnitType());
            storeOrderGood.setSubEntrustedOrder(subOrderNo);
            storeOrderGood.setMainEntrustedOrder(entrustedOrderNumber);
            storeOrderGoods.add(storeOrderGood);
        }
        baseMapper.insert(saveStoreOrders);
        storeOrderGoodsService.saveStoreOrderGoods(storeOrderGoods);
        // 订单服务商
        StoreProviderRelationVO storeProviderRelationVO = storeOrderDTO.getStoreProviderRelationVO();
        if(null != storeProviderRelationVO) {
            storeProviderRelationService.deleteByMainEntrustedOrder(entrustedOrderNumber);
            TmsStoreProviderRelationEntity saveBean = BeanUtil.toBean(storeProviderRelationVO, TmsStoreProviderRelationEntity.class);
            saveBean.setMainEntrustedOrder(entrustedOrderNumber);
            storeProviderRelationService.saveStoreOrderProvider(saveBean);
        }
        return true;
    }

    @Override
    public StoreOrderDetailVO getStoreOrderById(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            return null;
        }
        // 基本信息
        StoreOrderDetailVO detailVO = BeanUtil.toBean(storeOrder, StoreOrderDetailVO.class);
        String mainEntrustedNo = storeOrder.getEntrustedOrderNumber();
        // 基础费用
        TmsStoreProviderRelationEntity entity = storeProviderRelationService.getByMainEntrustedOrder(mainEntrustedNo);
        if(null != entity){
            StoreProviderRelationVO providerRelationVO = BeanUtil.toBean(entity, StoreProviderRelationVO.class);
            if(null != providerRelationVO){
                providerRelationVO.setFormula(providerRelationVO.getBaseFreightAmount()+"*"+providerRelationVO.getProfitRate()+"="+providerRelationVO.getFreightAmount());
            }
            detailVO.setStoreProviderRelationVO(providerRelationVO);
        }
        Map<String, TmsStoreOrderEntity> subOrderMap = baseMapper.getSubOrderMapByMainEntrustedOrder(mainEntrustedNo);
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(mainEntrustedNo);
        if(CollUtil.isNotEmpty(storeOrderGoods)){
            List<StoreOrderGoodsVO> goodsVOS = BeanUtil.copyToList(storeOrderGoods, StoreOrderGoodsVO.class);
            // 填充跟踪单号
            for (StoreOrderGoodsVO goodsVO : goodsVOS) {
                String subEntrustedOrder = goodsVO.getSubEntrustedOrder();
                if(null != subOrderMap){
                    TmsStoreOrderEntity storeOrderInfo = subOrderMap.get(subEntrustedOrder);
                    if(null != storeOrderInfo){
                        goodsVO.setExternalOrderNumber(storeOrderInfo.getExternalOrderNumber());
                    }
                }
            }
            detailVO.setStoreOrderGoods(goodsVOS);
            // 货物单位
            StoreOrderGoodsVO storeOrderGoodsVO = goodsVOS.get(0);
            detailVO.setUnitType(storeOrderGoodsVO.getUnitType());
        }
        // 异常信息
        List<TmsStoreOrderExceptionEntity> storeOrderExceptions = tmsStoreOrderExceptionService.getStoreOrderExceptionsByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderExceptions)){
            List<TmsStoreOrderExceptionVo> exceptionVOS = BeanUtil.copyToList(storeOrderExceptions, TmsStoreOrderExceptionVo.class);
            detailVO.setExceptionList(exceptionVOS);
        }
        // 包裹赔付信息
        List<TmsStoreOrderPkgPayEntity> storeOrderPkgPays = tmsStoreOrderPkgPayService.getStoreOrderPkgPaysByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderPkgPays)){
            List<TmsStoreOrderPkgPayVo> pkgPayVOS = BeanUtil.copyToList(storeOrderPkgPays, TmsStoreOrderPkgPayVo.class);
            detailVO.setPkgPayList(pkgPayVOS);
        }
        return detailVO;
    }

    @Override
    public List<ImportStoreGoodsDTO> importStoreGoods(MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            List<ImportStoreGoodsDTO> importData = new ArrayList<>();
            ExcelReader.Meta<ImportStoreGoodsDTO> meta = new ExcelReader.Meta<>();
            meta.setDomain(ImportStoreGoodsDTO.class);
            meta.setExcelStream(inputStream);
            meta.setHeadRowNumber(1);
            meta.setConsumer(importData::addAll);
            meta.setExceptionConsumer(ExcelValidatedException::new);
            ExcelReader excelReader = new ExcelReader(validator);
            excelReader.read(meta);
            boolean hasEmpty = importData
                    .stream()
                    .anyMatch(dto -> null == dto.getLength()
                            || null ==  dto.getWidth()
                            || null == dto.getHeight()
                            || null == dto.getWeight());
            if(hasEmpty){
                throw new CustomBusinessException("货物长度/宽度/高度/重量不能为空");
            }
            boolean overWeight = importData
                    .stream()
                    .anyMatch(e-> StoreConstants.WEIGHT_LIMIT_KG.compareTo(e.getWeight()) < 0);
            if(overWeight){
                throw new CustomBusinessException("重量不能超过68kg");
            }
            return importData;
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public ChannelPriceVO getSuggestChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO) {
        // 根据盲盒规则
        List<StoreOrderGoodsDTO> storeOrderGoods = channelPriceQueryDTO.getStoreOrderGoods();
        Integer unitType = channelPriceQueryDTO.getUnitType();
        verifyGoodsOverWeight(storeOrderGoods,unitType);
        // 获取询价信息
        ChannelPriceContentDTO channelPriceContent = getChannelPriceContent(storeOrderGoods, unitType);
        // 收-发货人邮编
        channelPriceContent.setReceiverPostalCode(channelPriceQueryDTO.getReceiverPostalCode());
        channelPriceContent.setShipperPostalCode(channelPriceQueryDTO.getShipperPostalCode());

        // 配置所有渠道
        List<TmsServiceProviderEntity> validChannel = serviceProviderService.getAllValid();
        if(CollUtil.isEmpty(validChannel)){
            throw new CustomBusinessException("无启用渠道,不支持询价");
        }
        // 获取启用的盲盒
        TmsBlindBoxEntity validBoxOne = blindBoxService.getValidBoxOne();
        if(null == validBoxOne){
            throw new CustomBusinessException("未找到比价配置,不支持询价");
        }
        List<TmsBlindBoxRuleEntity> boxRules = blindBoxRuleService.getRulesByBlindBoxId(validBoxOne.getId());
        if(CollUtil.isEmpty(boxRules)){
            throw new CustomBusinessException("比价规则规则未配置,不支持询价");
        }
        List<Long> providerIds = boxRules
                .stream()
                .map(TmsBlindBoxRuleEntity::getProviderId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, TmsServiceProviderEntity> serviceProviderMap = serviceProviderService.getMapByProviderIds(providerIds);
        if(CollUtil.isEmpty(serviceProviderMap)){
            throw new CustomBusinessException("服务商配置不存在,不支持询价");
        }
        Map<String,TmsBlindBoxRuleEntity> boxRulesMap = new ConcurrentHashMap<>();
        for (TmsBlindBoxRuleEntity boxRule : boxRules) {
            Long providerId = boxRule.getProviderId();
            TmsServiceProviderEntity serviceProvider = serviceProviderMap.get(providerId);
            if(null != serviceProvider && StrUtil.isNotBlank(serviceProvider.getProviderCode())){
                boxRulesMap.putIfAbsent(serviceProvider.getProviderCode(),boxRule);
            }
        }
        Map<String, TmsServiceProviderEntity> channelEntityMap = validChannel
                .stream()
                .collect(Collectors.toMap(TmsServiceProviderEntity::getProviderCode, Function.identity(), (v1, v2) -> v1));
        // NB渠道
        String channelCodesNB = getChannelCodesNB(validChannel);
        // 小包渠道
        List<String> channelCodesPackage = getChannelCodesPackage(validChannel);
        // 获取询价
        List<ChannelPriceVO> channelPrices = getChannelPrices(channelCodesNB, channelCodesPackage, channelPriceContent);
        if(CollUtil.isEmpty(channelPrices)){
            return null;
        }
        // 比价规则调价
        for (ChannelPriceVO channelPrice : channelPrices) {
            String providerCode = channelPrice.getProviderCode();
            if(StrUtil.isBlank(providerCode)){
                continue;
            }
            TmsBlindBoxRuleEntity boxRule = boxRulesMap.get(providerCode);
            if(null == boxRule){
                continue;
            }
            BigDecimal profitRate = boxRule.getProfitRate();
            BigDecimal baseFreightAmount = channelPrice.getBaseFreightAmount();
            // Precompute 1 + (profitRate / 100) in a single step
            BigDecimal multiplier = BigDecimal.ONE.add(profitRate.divide(BigDecimal.valueOf(100),2,RoundingMode.UP));
            BigDecimal freightAmount = baseFreightAmount.multiply(multiplier).setScale(2, RoundingMode.UP);
            channelPrice.setFreightAmount(freightAmount);
            channelPrice.setProfitRate(multiplier);
            channelPrice.setFormula(baseFreightAmount + "*" + multiplier +"=" + freightAmount);

            TmsServiceProviderEntity serviceProvider = channelEntityMap.get(providerCode);
            if(null != serviceProvider){
                channelPrice.setProviderName(serviceProvider.getProviderName());
            }
            channelPrice.setBoxCode(validBoxOne.getCode());
            channelPrice.setBoxName(validBoxOne.getName());
            channelPrice.setDefaultFlag(StoreConstants.ONE);
        }
        Optional<ChannelPriceVO> minFreightChannel = channelPrices.stream()
                .filter(cp -> cp.getFreightAmount() != null)
                .min(Comparator.comparing(ChannelPriceVO::getFreightAmount));
        return minFreightChannel.orElse(null);
    }

    @Override
    public List<ChannelPriceVO> getChannelPrice(ChannelPriceQueryDTO channelPriceQueryDTO) {
        Long storeCustomerId = channelPriceQueryDTO.getStoreCustomerId();
        if(null == storeCustomerId){
            Long userId = SecurityUtils.getUser().getId();
            storeCustomerId = storeCustomerService.getStoreCustomerIdByUserId(userId);
        }
        TmsStoreCustomerEntity storeCustomer = storeCustomerService.getStoreCustomerById(storeCustomerId);
        /**
         * TIER 1 - 刚注册的客户，享受我们快递成本价格*2的价格（前提我们价格不能超过官方指定价格）
         * TIER 2 - 半年内消费（或充值）满$500的客户，享受我们快递成本价格*1.75的折扣
         * TIER 3 - 半年内消费（或充值）满$2000的客户，享受我们快递成本价格*1.5的折扣
         * TIER 4 - 半年内消费（或充值）满$5000的客户，享受我们快递成本价格*1.2的折扣
         */
        BigDecimal profitRate = BigDecimal.valueOf(2.00);
        if(null != storeCustomer){
            Integer level = storeCustomer.getLevel();
            if(null != level){
                if(StoreEnums.StoreCustomer.Level.NORMAL.getValue().equals(level)){
                    profitRate = BigDecimal.valueOf(2.00);
                }else if(StoreEnums.StoreCustomer.Level.VIP1.getValue().equals(level)){
                    profitRate = BigDecimal.valueOf(1.75);
                }else if(StoreEnums.StoreCustomer.Level.VIP2.getValue().equals(level)){
                    profitRate = BigDecimal.valueOf(1.50);
                }else if(StoreEnums.StoreCustomer.Level.VIP3.getValue().equals(level)){
                    profitRate = BigDecimal.valueOf(1.20);
                }
            }
        }
        List<StoreOrderGoodsDTO> storeOrderGoods = channelPriceQueryDTO.getStoreOrderGoods();
        Integer unitType = channelPriceQueryDTO.getUnitType();
        verifyGoodsOverWeight(storeOrderGoods,unitType);
        // 获取询价信息
        ChannelPriceContentDTO channelPriceContent = getChannelPriceContent(storeOrderGoods, unitType);
        // 收-发货人邮编
        channelPriceContent.setReceiverPostalCode(channelPriceQueryDTO.getReceiverPostalCode());
        channelPriceContent.setShipperPostalCode(channelPriceQueryDTO.getShipperPostalCode());

        List<ChannelPriceVO> channelPriceVOS = new ArrayList<>();
        List<TmsServiceProviderEntity> validChannel = serviceProviderService.getAllValid();
        if(CollUtil.isEmpty(validChannel)){
            return channelPriceVOS;
        }
        Map<String, TmsServiceProviderEntity> channelEntityMap = validChannel
                .stream()
                .collect(Collectors.toMap(TmsServiceProviderEntity::getProviderCode, Function.identity(), (v1, v2) -> v1));

        // NB渠道
        String channelCodesNB = getChannelCodesNB(validChannel);
        // 小包渠道
        List<String> channelCodesPackage = getChannelCodesPackage(validChannel);

        List<ChannelPriceVO> channelPrices = getChannelPrices(channelCodesNB, channelCodesPackage, channelPriceContent);

        if(CollUtil.isEmpty(channelPrices)){
            return new ArrayList<>();
        }

        for (ChannelPriceVO channelPrice : channelPrices) {
            String providerCode = channelPrice.getProviderCode();
            if(StrUtil.isBlank(providerCode)){
                continue;
            }
            BigDecimal baseFreightAmount = channelPrice.getBaseFreightAmount();
            BigDecimal freightAmount = baseFreightAmount.multiply(profitRate).setScale(2, RoundingMode.UP);
            channelPrice.setFreightAmount(freightAmount);
            channelPrice.setProfitRate(profitRate);
            channelPrice.setFormula(baseFreightAmount + "*" + profitRate +"=" + freightAmount);
            TmsServiceProviderEntity serviceProvider = channelEntityMap.get(providerCode);
            if(null != serviceProvider){
                channelPrice.setProviderName(serviceProvider.getProviderName());
            }
            channelPrice.setDefaultFlag(StoreConstants.ZERO);

        }
        // 按价格升序排序
        return channelPrices.stream().sorted(Comparator.comparing(ChannelPriceVO::getFreightAmount)).collect(Collectors.toList());
    }

    /**
     * 询价上下文
     * @param storeOrderGoods
     * @param unitType
     * @return
     */
    private ChannelPriceContentDTO getChannelPriceContent(List<StoreOrderGoodsDTO> storeOrderGoods,Integer unitType){
        if(CollUtil.isEmpty(storeOrderGoods)){
            throw new CustomBusinessException("货物信息不能为空");
        }
        BigDecimal totalLength = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getLength)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWidth = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getWidth)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalHeight = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getHeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalWeight = storeOrderGoods.stream()
                .map(StoreOrderGoodsDTO::getWeight)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        unitType = (unitType == null) ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
        // 法英制度 单位换算
        if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
            totalLength = UnitConvertUtil.volumeConvert(totalLength,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWidth  = UnitConvertUtil.volumeConvert(totalWidth,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalHeight = UnitConvertUtil.volumeConvert(totalHeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
        }

        ChannelPriceContentDTO channelPriceContentDTO = new ChannelPriceContentDTO();
        channelPriceContentDTO.setTotalLength(totalLength);
        channelPriceContentDTO.setTotalWidth(totalWidth);
        channelPriceContentDTO.setTotalHeight(totalHeight);
        channelPriceContentDTO.setTotalWeight(totalWeight);
        BigDecimal volumeCM3 = totalLength.multiply(totalWidth).multiply(totalHeight);
        channelPriceContentDTO.setTotalVolume(UnitConvertUtil.volumeCM3ConvertToM3(volumeCM3));
        return channelPriceContentDTO;
    }

    /**
     * 获取NB渠道
     * @param channelCodes
     * @return
     */
    private String getChannelCodesNB(List<TmsServiceProviderEntity> channelCodes){
        if(CollUtil.isEmpty(channelCodes)){
            return null;
        }
        List<String> matched = channelCodes
                .stream()
                .filter(Objects::nonNull)
                .map(TmsServiceProviderEntity::getProviderCode)
                .filter(code -> code.equals(StoreConstants.NB_SERVICE_PROVIDER))
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(matched)){
            return null;
        }
        return matched.get(0);
    }

    /**
     * 获取小包渠道
     * @param channelCodes
     * @return
     */
    private List<String> getChannelCodesPackage(List<TmsServiceProviderEntity> channelCodes){
        if(CollUtil.isEmpty(channelCodes)){
            return null;
        }
        List<String> matched = channelCodes
                .stream()
                .filter(Objects::nonNull)
                .map(TmsServiceProviderEntity::getProviderCode)
                .filter(code -> code.startsWith(StoreConstants.CA))
                .distinct()
                .collect(Collectors.toList());

        if(CollUtil.isEmpty(matched)){
            return null;
        }
        return matched;
    }

    private List<ChannelPriceVO> getChannelPrices(String channelCodesNB,List<String> channelCodesPackage,ChannelPriceContentDTO channelPriceContent){
        List<ChannelPriceVO> channelPrices = new ArrayList<>();
        if(StrUtil.isNotBlank(channelCodesNB)){
            try {
                PriceCalculationRequestVo priceCalculationRequest = getPriceCalculationRequestVo(channelCodesNB, channelPriceContent);
                PriceCalculationResultVo priceCalculationResultVo = tmsReceivableService.calculatePrice(priceCalculationRequest);
                if(null != priceCalculationResultVo) {
                    BigDecimal totalPrice = priceCalculationResultVo.getTotalPrice();
                    if(null != totalPrice && totalPrice.compareTo(BigDecimal.ZERO) > 0){
                        ChannelPriceVO channelPriceVO = new ChannelPriceVO();
                        channelPriceVO.setProviderCode(channelCodesNB);
                        channelPriceVO.setBaseFreightAmount(totalPrice);
                        channelPrices.add(channelPriceVO);
                    }
                }
                if(logger.isInfoEnabled()){
                    logger.info("[服务商询价]NB询价,params:{},result={}",priceCalculationRequest,priceCalculationResultVo);
                }
            }catch (Exception e){
                logger.error("[服务商询价]NB询价,操作失败",e);
            }
        }
        if(CollUtil.isNotEmpty(channelCodesPackage)) {
            try {
                FeeRequest feeRequest = new FeeRequest();
                feeRequest.setLength(channelPriceContent.getTotalLength());
                feeRequest.setWidth(channelPriceContent.getTotalWidth());
                feeRequest.setHeight(channelPriceContent.getTotalHeight());
                feeRequest.setWeight(channelPriceContent.getTotalWeight());
                feeRequest.setPostCode(channelPriceContent.getReceiverPostalCode());
                feeRequest.setChannelCode(channelCodesPackage);
                feeRequest.setIso2(StoreConstants.CA);
                ResponseResult<FreightRoot> resultPackage = postBusinessUtils.calculateFreight(feeRequest);
                if (logger.isInfoEnabled()) {
                    logger.info("[服务商询价]小包询价,params:{},result={}", feeRequest, resultPackage);
                }
                if (null != resultPackage &&  null != resultPackage.getData()) {
                    FreightRoot freightRoot = resultPackage.getData();
                    List<FreightDataItem> data = freightRoot.getData();
                    if (CollUtil.isNotEmpty(data)) {
                        for (FreightDataItem item : data) {
                            String totalFee = item.getTotalFee();
                            if (StrUtil.isBlank(totalFee)) {
                                continue;
                            }
                            BigDecimal totalFeeAmount = BigDecimal.valueOf(Double.parseDouble(totalFee));
                            if (totalFeeAmount.compareTo(BigDecimal.ZERO) > 0) {
                                ChannelPriceVO channelPriceVO = new ChannelPriceVO();
                                channelPriceVO.setProviderCode(item.getChannelCode());
                                channelPriceVO.setBaseFreightAmount(totalFeeAmount);
                                channelPrices.add(channelPriceVO);
                            }
                        }
                    }
                }
            }catch (Exception e){
                logger.error("[服务商询价]小包询价,操作失败",e);
            }
        }
        return channelPrices;
    }

    private PriceCalculationRequestVo getPriceCalculationRequestVo(String channelCodesNB, ChannelPriceContentDTO channelPriceContent) {
        TmsOrderPriceCalculationVo channelPriceNB = new TmsOrderPriceCalculationVo();
        channelPriceNB.setDestPostalCode(channelPriceContent.getReceiverPostalCode());
        channelPriceNB.setShipperPostalCode(channelPriceContent.getShipperPostalCode());
        channelPriceNB.setTotalWeight(channelPriceContent.getTotalWeight());
        channelPriceNB.setTotalVolume(channelPriceContent.getTotalVolume());
        TmsCustomerEntity tmsCustomerEntity = customerService.getCustomerByCustomerName(StoreConstants.NB_EXPRESS_CUSTOMER_NAME);
        if(null != tmsCustomerEntity){
            channelPriceNB.setCustomerId(tmsCustomerEntity.getId());
        }
        PriceCalculationRequestVo priceCalculationRequest = new PriceCalculationRequestVo();
        priceCalculationRequest.setOrders(Collections.singletonList(channelPriceNB));
        priceCalculationRequest.setProviderName(channelCodesNB);
        return priceCalculationRequest;
    }


    @Override
    public SuggestStoreDetailVO getSuggestStore(SuggestStoreQueryDTO storeQueryDTO) {
        /**
         * 1. 验证参数
         * 2. 获取启用的门店信息，剔除经纬度为空的门店
         * 3. 计算发货地的经纬度与门店的经纬度距离,默认计算所有的门店(门店可控)
         * 4. 根据距离进行排序，最近的门店为推荐门店
         */
        SuggestStoreDetailVO suggestStoreDetailVO = new SuggestStoreDetailVO();
        List<TmsStoreEntity> allStore = storeService.getAllStoreEntity();
        if(CollUtil.isEmpty(allStore)){
            return suggestStoreDetailVO;
        }
        List<TmsStoreEntity> filterStore = allStore.stream()
                .filter(e->ObjectUtil.isNotEmpty(e.getStoreLatLng()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(filterStore)){
            return suggestStoreDetailVO;
        }

        String shipperOrigin = storeQueryDTO.getShipperOrigin();
        String shipperAddress = storeQueryDTO.getShipperAddress();
        String shipperPostalCode = storeQueryDTO.getShipperPostalCode();
        String finalAddress = AddressUtil.normalizeAddress(shipperAddress);
        String addressDetail = finalAddress + " " + shipperPostalCode;
        try {
            String latLngByAddress = routePlanService.getLatLngByAddress(addressDetail);
            if(StrUtil.isBlank(latLngByAddress)){
                return suggestStoreDetailVO;
            }
            String[] split = latLngByAddress.split(",");
            if(split.length != 2){
                return suggestStoreDetailVO;
            }
            double lat = Double.parseDouble(split[0]);
            double lng = Double.parseDouble(split[1]);

            suggestStoreDetailVO.setShipperOrigin(shipperOrigin);
            suggestStoreDetailVO.setShipperAddress(shipperAddress);
            suggestStoreDetailVO.setShipperPostalCode(shipperPostalCode);
            suggestStoreDetailVO.setLat(lat);
            suggestStoreDetailVO.setLng(lng);
            // 根据经纬度计算距离
            List<SuggestStoreVO> suggestStores = new ArrayList<>();
            HaversineUtil.LatLng p1 = new HaversineUtil.LatLng(lat,lng);
            for (TmsStoreEntity item : filterStore) {
                String[] splitItem = item.getStoreLatLng().split(",");
                if(splitItem.length != 2){
                    continue;
                }
                double destLat = Double.parseDouble(splitItem[0]);
                double destLng = Double.parseDouble(splitItem[1]);
                HaversineUtil.LatLng p2 = new HaversineUtil.LatLng(destLat,destLng);
                double distance = HaversineUtil.distanceKm(p1, p2);

                SuggestStoreVO suggestStoreVO = new SuggestStoreVO();
                suggestStoreVO.setId(item.getId());
                suggestStoreVO.setStoreAddress(item.getStoreAddress());
                suggestStoreVO.setStoreName(item.getStoreName());
                suggestStoreVO.setStoreCode(item.getStoreCode());
                suggestStoreVO.setDistance(distance);
                suggestStoreVO.setLat(destLat);
                suggestStoreVO.setLng(destLng);
                suggestStores.add(suggestStoreVO);
            }
            // 推荐前11，第一个默认为推荐门店
            List<SuggestStoreVO> suggest = suggestStores.stream()
                    .filter(e -> ObjectUtil.isNotNull(e.getDistance()))
                    .sorted(Comparator.comparingDouble(SuggestStoreVO::getDistance))
                    .limit(11)
                    .collect(Collectors.toList());
            suggestStoreDetailVO.setSuggestStores(suggest);
        }catch (Exception e){
            logger.error("推荐门店计算异常",e);
        }
        return suggestStoreDetailVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void printScript(Long id, HttpServletResponse response) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            // TODO 国际化处理
            throw new CustomBusinessException("订单不存在,不支持操作");
        }
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        try {
            InputStream inputStream = new URL(StoreConstants.TemplateConstants.OrderRedemptionVoucher).openStream();
            PDDocument document = PDDocument.load(inputStream);
            PDPage page = document.getPage(StoreConstants.ZERO);
            // 2. 获取表单
            PDAcroForm acroForm = document.getDocumentCatalog().getAcroForm();
            // 3.填充文本
            PDField orderField = acroForm.getField(StoreConstants.TemplateVariableConstants.ENTRUSTED_ORDER_NUMBER);
            if (orderField != null) {
                orderField.setValue(entrustedOrderNumber);
            }
            BufferedImage qrImage = GenerateUtils.generateQRCode(entrustedOrderNumber, 350, 300);
            BufferedImage barImage = GenerateUtils.generateBarCode(entrustedOrderNumber, 350, 100);
            // 3. 获取图片字段的位置信息
            GenerateUtils.insertImageToField(document, page, acroForm, StoreConstants.TemplateVariableConstants.QR_ORDER_IMAGE, qrImage);
            GenerateUtils.insertImageToField(document, page, acroForm, StoreConstants.TemplateVariableConstants.BAR_ORDER_IMAGE, barImage);
            acroForm.flatten();
            // 5. 输出PDF
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=\"RedemptionVoucher.pdf\"");
            document.save(response.getOutputStream());
            document.close();
        }catch (Exception e){
            logger.error("[快递业务订单][打印凭证]",e);
            // TODO 国际化处理
            throw new CustomBusinessException("打印凭证失败");
        }
        LocalDateTime printTime = LocalDateTime.now();
        TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
        updateEntity.setId(storeOrder.getId());
        updateEntity.setPrintTime(printTime);
        updateEntity.setPrintStatus(StoreEnums.StoreOrder.PrintStatus.PRINTED.getValue());
        baseMapper.updateById(updateEntity);
        // 子单的打印状态需更新
        List<TmsStoreOrderEntity> subStoreOrder = baseMapper.getSubOrderByMainEntrustedOrder(entrustedOrderNumber);
        if(CollUtil.isNotEmpty(subStoreOrder)){
            List<TmsStoreOrderEntity> updateSubEntity =  new ArrayList<>();
            for (TmsStoreOrderEntity storeOrderItem : subStoreOrder) {
                TmsStoreOrderEntity subEntity = new TmsStoreOrderEntity();
                subEntity.setId(storeOrderItem.getId());
                subEntity.setPrintTime(printTime);
                subEntity.setPrintStatus(StoreEnums.StoreOrder.PrintStatus.PRINTED.getValue());
                updateSubEntity.add(subEntity);
            }
            baseMapper.updateById(updateSubEntity);
        }
    }

    @Override
    public IPage<StoreOrderVO> getTmsStoreOrderStorePage(StoreOrderQueryDTO storeOrderQueryDTO) {
        Long userId = SecurityUtils.getUser().getId();
        // 获取门店ID
        TmsStoreUserEntity storeUser = storeUserService.getOne(Wrappers.<TmsStoreUserEntity>lambdaQuery().eq(TmsStoreUserEntity::getUserId, userId));
        if (storeUser == null) {
            return new Page<>();
        }
        Long storeId = storeUser.getStoreId();
        if (storeId == null) {
            return new Page<>();
        }
        Page<TmsStoreOrderEntity> page = new Page<>(storeOrderQueryDTO.getCurrent(),storeOrderQueryDTO.getSize());
        // 构建分页条件
        return this.baseMapper.getTmsStoreOrderStorePage(page, storeOrderQueryDTO, storeId);
    }

    @Override
    public IPage<StoreOrderVO> getTmsStoreOrderAdminPage(StoreOrderQueryDTO storeOrderQueryDTO) {
        Page<TmsStoreOrderEntity> page = new Page<>(storeOrderQueryDTO.getCurrent(),storeOrderQueryDTO.getSize());
        return this.baseMapper.getTmsStoreOrderAdminPage(page, storeOrderQueryDTO);
    }


    // 推送订单到小包系统 - 返回外部系统跟踪单号
    private PackageOrderDataItem pushOrderToPacket(TmsStoreOrderEntity storeOrder, TmsStoreOrderGoodsEntity storeOrderGood, String providerCode, String entrustedOrderNumber){
        // 是否购买保险
        Integer insured = storeOrder.getInsuranceSign();
        // 件数
        Integer pieces = StoreConstants.ONE;
        BigDecimal totalWeight = storeOrderGood.getWeight();
        BigDecimal totalLength = storeOrderGood.getLength();
        BigDecimal totalWidth = storeOrderGood.getWidth();
        BigDecimal totalHeight = storeOrderGood.getHeight();
        Integer unitType = storeOrderGood.getUnitType();
        unitType = (unitType == null) ? UnitConvertUtil.UNIT_INTERNATIONAL : unitType;
        // 法英制度 单位换算
        if(UnitConvertUtil.UNIT_BRITAIN.equals(unitType)){
            totalLength = UnitConvertUtil.volumeConvert(totalLength,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWidth  = UnitConvertUtil.volumeConvert(totalWidth,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalHeight = UnitConvertUtil.volumeConvert(totalHeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
            totalWeight = UnitConvertUtil.weightConvert(totalWeight,UnitConvertUtil.UNIT_BRITAIN,UnitConvertUtil.UNIT_INTERNATIONAL);
        }

        // 收件人必填信息
        String consigneeName = storeOrder.getReceiverName();
        String receiverDest = storeOrder.getReceiverDest();
        String[] splitDest = receiverDest.split(StoreConstants.ADDRESS_SPLIT_SIGN);
        if(splitDest.length != 3){
            return null;
        }
        String consigneeCountryCode = splitDest[0];
        String consigneeProvince = splitDest[1];
        String consigneeCity = splitDest[2];
        String consigneeAddress = storeOrder.getReceiverAddress();
        String consigneePostcode = storeOrder.getReceiverPostalCode();
        String consigneePhone = storeOrder.getReceiverPhone();

        // 发货人信息
        String shipperName = storeOrder.getShipperName();
        String shipperOrigin = storeOrder.getShipperOrigin();
        String[] splitOrigin = shipperOrigin.split(StoreConstants.ADDRESS_SPLIT_SIGN);
        if(splitOrigin.length != 3){
            return null;
        }
        String shipperCountryCode = splitOrigin[0];
        String shipperProvince = splitOrigin[1];
        String shipperCity = splitOrigin[2];
        String shipperAddress = storeOrder.getReceiverAddress();
        String shipperPostcode = storeOrder.getReceiverPostalCode();
        String shipperPhone = storeOrder.getReceiverPhone();

        // 申报明细 - 固定写死
        ApiOrderItemVO apiOrderItemVO = new ApiOrderItemVO();
        apiOrderItemVO.setEname(StoreConstants.OrderItemConstants.ENAME);
        apiOrderItemVO.setCname(StoreConstants.OrderItemConstants.CNAME);
        apiOrderItemVO.setSku(StoreConstants.OrderItemConstants.SKU);
        apiOrderItemVO.setPrice(StoreConstants.OrderItemConstants.PRICE);
        apiOrderItemVO.setWeight(totalWeight);
        apiOrderItemVO.setQuantity(StoreConstants.OrderItemConstants.QUANTITY);

        // 构建材积参数 - 考虑核算一个 -适配小包的一票一件
        ApiOrderVolumeVO apiOrderVolumeVO = new ApiOrderVolumeVO();
        apiOrderVolumeVO.setLength(totalLength);
        apiOrderVolumeVO.setWidth(totalWidth);
        apiOrderVolumeVO.setHeight(totalHeight);
        apiOrderVolumeVO.setQuantity(pieces);
        apiOrderVolumeVO.setRweight(totalWeight);

        OrderRequest orderRequest = new OrderRequest();
        orderRequest.setChannelCode(providerCode);
        orderRequest.setReferenceNo(entrustedOrderNumber);
        orderRequest.setProductType(StoreConstants.OrderItemConstants.PRODUCT_TYPE);
        orderRequest.setPweight(totalWeight);
        orderRequest.setPieces(pieces);
        orderRequest.setInsured(insured);
        orderRequest.setConsigneeName(consigneeName);
        orderRequest.setConsigneeCountryCode(StoreConstants.CA);
        orderRequest.setReturnLabel(StoreConstants.OrderItemConstants.RETURN_LABEL);
        // orderRequest.setConsigneeCountryCode(consigneeCountryCode);
        orderRequest.setConsigneeProvince(consigneeProvince);
        orderRequest.setConsigneeCity(consigneeCity);
        orderRequest.setConsigneeAddress(consigneeAddress);
        orderRequest.setConsigneePostcode(consigneePostcode);
        orderRequest.setConsigneePhone(consigneePhone);
        orderRequest.setShipperName(shipperName);
        orderRequest.setShipperCountryCode(StoreConstants.CA);
        // orderRequest.setShipperCountryCode(shipperCountryCode);
        orderRequest.setShipperProvince(shipperProvince);
        orderRequest.setShipperCity(shipperCity);
        orderRequest.setShipperAddress(shipperAddress);
        orderRequest.setShipperPostcode(shipperPostcode);
        orderRequest.setShipperPhone(shipperPhone);

        orderRequest.setApiOrderItemList(Collections.singletonList(apiOrderItemVO));
        orderRequest.setApiOrderVolumeList(Collections.singletonList(apiOrderVolumeVO));
        ResponseResult<PackageOrderRoot> request = postBusinessUtils.createRequest(orderRequest);
        if(null != request){
            int code = request.getCode();
            if(StoreConstants.ZERO == code){
                throw new CustomBusinessException("小包系统推单失败:"+ request.getMessage());
            }
            return request.getData().getData();
        }
        return null;
    }

    // 推送订单到NB系统 - 返回NB系统主单号作为跟踪单号
    private ZdjApiVo pushOrderToNB(List<TmsStoreOrderGoodsEntity> storeOrderGoods,TmsStoreOrderEntity storeOrder,String mainOrderNumber){
        TmsCustomerEntity tmsCustomerEntity = customerService.getCustomerByCustomerName(StoreConstants.NB_EXPRESS_CUSTOMER_NAME);
        if(null == tmsCustomerEntity){
            throw new CustomBusinessException("NB系统快递业务客户不存在,推送订单失败");
        }
        /**
         * 产品提 写死
         * 运输类型：零担运输
         * 订单类型：包裹
         * 货物类型：普通货品
         * 业务模式：中大件
         * 收货方式：根据快递所选则的方式
         */
        // NB系统快递业务客户id -委托客户
        Long nbCustomerId = tmsCustomerEntity.getId();

        TmsCustomerOrderEntity tmsCustomerOrder = new TmsCustomerOrderEntity();
        tmsCustomerOrder.setCustomerId(nbCustomerId);
        tmsCustomerOrder.setCustomerOrderNumber(mainOrderNumber);
        // 运输类型：零担运输
        tmsCustomerOrder.setTransportType(StoreConstants.TWO);
        // 货物类型：普通货品
        tmsCustomerOrder.setCargoType(StoreConstants.ONE);
        // 订单类型：包裹
        tmsCustomerOrder.setOrderType(StoreConstants.TWO);
        // 业务模式: 中大件
        tmsCustomerOrder.setBusinessModel(StoreConstants.TWO);
        // 发货方式
        Integer sendType = storeOrder.getSendType();
        if(StoreEnums.StoreOrder.SendType.DELIVERY_TO_STORE.getValue().equals(sendType)){
            tmsCustomerOrder.setReceiveType(StoreConstants.ONE);
        }else if(StoreEnums.StoreOrder.SendType.COLLECTING_FROM_NB.getValue().equals(sendType)){
            tmsCustomerOrder.setReceiveType(StoreConstants.TWO);
        }
        // 发货信息
        String shipperName = storeOrder.getShipperName();
        String shipperPhone = storeOrder.getShipperPhone();
        // 始发地
        String shipperOrigin = storeOrder.getShipperOrigin();
        String shipperPostalCode = storeOrder.getShipperPostalCode();
        String shipperAddress = storeOrder.getShipperAddress();
        tmsCustomerOrder.setShipperName(shipperName);
        tmsCustomerOrder.setShipperPhone(shipperPhone);
        tmsCustomerOrder.setShipperAddress(shipperAddress);
        tmsCustomerOrder.setShipperPostalCode(shipperPostalCode);
        tmsCustomerOrder.setOrigin(shipperOrigin);

        // 收件信息
        String receiverName = storeOrder.getReceiverName();
        String receiverPhone = storeOrder.getReceiverPhone();
        String receiverDest = storeOrder.getReceiverDest();
        String receiverPostalCode = storeOrder.getReceiverPostalCode();
        String receiverAddress = storeOrder.getReceiverAddress();
        tmsCustomerOrder.setReceiverName(receiverName);
        tmsCustomerOrder.setReceiverPhone(receiverPhone);
        tmsCustomerOrder.setDestination(receiverDest);
        tmsCustomerOrder.setDestAddress(receiverAddress);
        tmsCustomerOrder.setDestPostalCode(receiverPostalCode);
        // 备注
        tmsCustomerOrder.setRemark(storeOrder.getRemark());
        // 单位
        TmsStoreOrderGoodsEntity tmsStoreOrderGoodsEntity = storeOrderGoods.get(0);
        Integer unitType = tmsStoreOrderGoodsEntity.getUnitType();
        tmsCustomerOrder.setUnits(unitType);

        List<TmsCargoInfoEntity> cargoInfoEntityList = new ArrayList<>(storeOrderGoods.size());

        for (TmsStoreOrderGoodsEntity storeOrderGood : storeOrderGoods) {
            BigDecimal length = storeOrderGood.getLength();
            BigDecimal width = storeOrderGood.getWidth();
            BigDecimal height = storeOrderGood.getHeight();
            BigDecimal weight = storeOrderGood.getWeight();
            String remark = storeOrderGood.getRemark();
            String goodInfo = storeOrderGood.getGoodInfo();

            TmsCargoInfoEntity cargoInfoEntity = new TmsCargoInfoEntity();
            cargoInfoEntity.setLength(length);
            cargoInfoEntity.setWidth(width);
            cargoInfoEntity.setHeight(height);
            cargoInfoEntity.setWeight(weight);
            cargoInfoEntity.setRemark(remark);
            cargoInfoEntity.setCargoDescription(goodInfo);
            // 箱号比较关键,映射子单
            cargoInfoEntity.setBoxNum(storeOrderGood.getSubEntrustedOrder());
            cargoInfoEntityList.add(cargoInfoEntity);
        }
        tmsCustomerOrder.setCargoInfoEntityList(cargoInfoEntityList);
        try {
            logger.info("[快递端业务下单][推送NB系统]跟踪单号:{},请求参数:{}",mainOrderNumber,tmsCustomerOrder);
            R zdjOrder = customerOrderService.createZDJOrder(tmsCustomerOrder, Boolean.TRUE);
            logger.info("[快递端业务下单][推送NB系统]跟踪单号:{},请求结果:{}",mainOrderNumber,zdjOrder);
            if(null == zdjOrder){
                throw new CustomBusinessException("[快递端业务下单][推送NB系统] 响应数据为空,推送失败");
            }
            if(zdjOrder.isOk()){
                Object data = zdjOrder.getData();
                if(ObjectUtil.isNotNull(data)){
                    String json = mapper.writeValueAsString(data);
                    return mapper.readValue(json, ZdjApiVo.class);
                }
            }else{
                String msg = zdjOrder.getMsg();
                throw new CustomBusinessException("[快递端业务下单][推送NB系统] " + msg);
            }
        }catch (Exception e){
           logger.error("[快递端业务下单][推单NB]推送失败,params={}",tmsCustomerOrder,e);
           throw new CustomBusinessException("[快递端业务下单][推单NB] 推送异常");
        }
        return null;
    }

    @Override
    public R<List<TmsImportStoreDTO>> importCargoInfo(MultipartFile file) {
        List<String> errorMessages = new ArrayList<>();
        List<TmsImportStoreDTO> dataList = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), TmsImportStoreDTO.class, new PageReadListener<TmsImportStoreDTO>((List<TmsImportStoreDTO> dataBatch) -> {
                int rowIndexStart = 0; // 批次起始行号
                for (TmsImportStoreDTO data : dataBatch) {
                    int batchRowIndex = ++rowIndexStart; // 当前行号
                    // 校验字段，若为空则加入 errorMessages
                    validateField(data.getShipperOrigin(), "始发地", batchRowIndex, errorMessages);
                    validateField(data.getReceiverDest(), "目的地", batchRowIndex, errorMessages);
                    validateField(data.getShipperName(), "发件联系人", batchRowIndex, errorMessages);
                    validateField(data.getShipperPhone(), "发件人电话", batchRowIndex, errorMessages);
                    validateField(data.getShipperAddress(), "发件详细地址", batchRowIndex, errorMessages);
                    validateField(data.getShipperPostalCode(), "发件邮编", batchRowIndex, errorMessages);
                    validateField(data.getReceiverName(), "收件联系人", batchRowIndex, errorMessages);
                    validateField(data.getReceiverPhone(), "收件人电话", batchRowIndex, errorMessages);
                    validateField(data.getReceiverAddress(), "收件详细地址", batchRowIndex, errorMessages);
                    validateField(data.getReceiverPostalCode(), "收件邮编", batchRowIndex, errorMessages);
                    validateField(data.getLength().toString(), "长", batchRowIndex, errorMessages);
                    validateField(data.getWidth().toString(), "宽", batchRowIndex, errorMessages);
                    validateField(data.getHeight().toString(), "高", batchRowIndex, errorMessages);
                    validateField(data.getWeight().toString(), "重量", batchRowIndex, errorMessages);

                    // 重量限制校验
                    if (data.getWeight() != null && data.getWeight().compareTo(new BigDecimal("68")) > 0) {
                        errorMessages.add("第【" + batchRowIndex + "】行：重量不能超过68KG");
                    }

                    data.convertSignFlags();

                    // 处理数据
                    dataList.add(data);
                }
            })).sheet().doRead();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 统一抛出错误信息
        if (!errorMessages.isEmpty()) {
            return R.failed("导入出现错误：" + String.join("; ", errorMessages));
        }

        return R.ok(dataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(storeOrder == null){
            throw new CustomBusinessException("订单不存在，不支持操作");
        }
        Integer orderStatus = storeOrder.getOrderStatus();
        Integer writeOffFlag = storeOrder.getWriteOffFlag();
        if(!StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue().equals(orderStatus)
        && !StoreEnums.StoreOrder.WriteOffFlag.UnWriteOff.getValue().equals(writeOffFlag)){
            throw new CustomBusinessException("订单状态非待发货且已核销状态,不支持取消订单");
        }
        TmsStoreOrderEntity updateEntity = new TmsStoreOrderEntity();
        updateEntity.setId(id);
        updateEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
        baseMapper.updateById(updateEntity);
        String mainEntrustedNo = updateEntity.getMainEntrustedOrder();
        baseMapper.updateOrderStatusByMainEntrustedNo(mainEntrustedNo,StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
        // 轨迹跟踪
        List<TmsStoreOrderEntity> subStoreOrder = baseMapper.getSubOrderByMainEntrustedOrder(mainEntrustedNo);
        if(CollUtil.isNotEmpty(subStoreOrder)){
            // 轨迹
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrder.size());
            for (TmsStoreOrderEntity item : subStoreOrder) {
                TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.CANCEL.getValue());
                traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.CANCEL.getEName());
                traceEntity.setSubEntrustedOrder(item.getEntrustedOrderNumber());
                traceEntity.setMainEntrustedOrder(item.getMainEntrustedOrder());
                storeOrderTraces.add(traceEntity);
            }
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
            // 站内消息推送
            List<String> businessKeys = subStoreOrder
                    .stream()
                    .map(TmsStoreOrderEntity::getEntrustedOrderNumber)
                    .distinct()
                    .collect(Collectors.toList());
            // 站内消息推送
            storeMessageTraceService.saveStoreMessageTrace(storeOrder.getStoreCustomerId(),StoreEnums.StoreMessageTrace.BusinessSubType.ORDER_CANCEL.getValue(),businessKeys);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized boolean writeOffOrder(Long id) {
        TmsStoreOrderEntity storeOrder = baseMapper.selectById(id);
        if(null == storeOrder){
            throw new CustomBusinessException("订单不存在,不支持操作");
        }
        if(!StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue().equals(storeOrder.getOrderStatus())){
            throw new CustomBusinessException("订单状态非待发货,不支持操作");
        }
        if(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue().equals(storeOrder.getWriteOffFlag())){
            throw new CustomBusinessException("订单已核销,不支持操作");
        }
        Long userId = SecurityUtils.getUser().getId();
        id = storeOrder.getId();
        String mainEntrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        BigDecimal freightAmount = storeOrder.getTotalFreightAmount();
        TmsStoreBalanceEntity storeBalance = storeBalanceService.getByStoreCustomerId(storeOrder.getStoreCustomerId());
        if(null == storeBalance){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额异常,不支持核销");
        }
        BigDecimal amount = storeBalance.getAmount();
        if(freightAmount.compareTo(amount) > 0){
            throw new CustomBusinessException(StoreConstants.BALANCE_ERROR_CODE,"客户余额不足,请前往充值");
        }
        storeBalanceService.executeOrderDeduction(storeBalance,freightAmount,mainEntrustedOrderNumber);

        // 获取所有货物信息
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(mainEntrustedOrderNumber);
        TmsStoreProviderRelationEntity storeProviderRelationVO = storeProviderRelationService.getByMainEntrustedOrder(mainEntrustedOrderNumber);
        TmsStoreUserEntity storeUser = storeUserService.getByUserId(userId);
        Long storeId = storeUser != null ? storeUser.getStoreId() : null;
        String providerCode = storeProviderRelationVO.getProviderCode();
        String entrustedOrderNumber = storeOrder.getEntrustedOrderNumber();
        LocalDateTime pushTime = LocalDateTime.now();
        if(StoreConstants.NB_SERVICE_PROVIDER.equals(providerCode)){
            // 推送NB订单
            ZdjApiVo zdjApiVo = pushOrderToNB(storeOrderGoods, storeOrder, mainEntrustedOrderNumber);
            if(null == zdjApiVo){
                throw new CustomBusinessException("推送NB系统订单失败,核销失败");
            }
            // 主单的跟踪单号 保持一致性
            String trackingNo = zdjApiVo.getTrackingNo();
            TmsStoreOrderEntity mainStoreOrder = new TmsStoreOrderEntity();
            mainStoreOrder.setId(id);
            mainStoreOrder.setStoreId(storeId);
            mainStoreOrder.setExternalOrderNumber(trackingNo);
            mainStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
            mainStoreOrder.setPushTime(pushTime);
            mainStoreOrder.setWriteOffStoreId(storeId);
            mainStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
            mainStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.NB.getValue());
            baseMapper.updateById(mainStoreOrder);

            Map<String, TmsStoreOrderEntity> subStoreOrderMap = baseMapper.getSubOrderMapByMainEntrustedOrder(mainEntrustedOrderNumber);
            HashMap<String, String> subOrderNosMap = zdjApiVo.getSubOrderNos();
            List<TmsStoreOrderEntity> subStoreOrderUpdate = new ArrayList<>(subStoreOrderMap.size());
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrderMap.size());
            for (Map.Entry<String, TmsStoreOrderEntity> entityEntry : subStoreOrderMap.entrySet()) {
                TmsStoreOrderEntity subStoreOrderEntity = entityEntry.getValue();
                String subTrackingNo = subOrderNosMap.get(subStoreOrderEntity.getEntrustedOrderNumber());
                if(StrUtil.isNotBlank(subTrackingNo)){
                    TmsStoreOrderEntity subStoreOrder = new TmsStoreOrderEntity();
                    subStoreOrder.setId(subStoreOrderEntity.getId());
                    subStoreOrder.setStoreId(storeId);
                    subStoreOrder.setExternalOrderNumber(subTrackingNo);
                    subStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    subStoreOrder.setPushTime(pushTime);
                    subStoreOrder.setWriteOffStoreId(storeId);
                    subStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
                    subStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.NB.getValue());
                    subStoreOrderUpdate.add(subStoreOrder);

                    TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                    traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                    traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getEName());
                    traceEntity.setSubEntrustedOrder(subStoreOrderEntity.getEntrustedOrderNumber());
                    traceEntity.setMainEntrustedOrder(subStoreOrderEntity.getMainEntrustedOrder());
                    storeOrderTraces.add(traceEntity);
                }
            }
            baseMapper.updateById(subStoreOrderUpdate);
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }else{
            Map<String, TmsStoreOrderEntity> subStoreOrderMap = baseMapper.getSubOrderMapByMainEntrustedOrder(entrustedOrderNumber);
            Map<String, TmsStoreOrderGoodsEntity> mapByMainEntrustedOrder = storeOrderGoodsService.getMapByMainEntrustedOrder(entrustedOrderNumber);
            if(null == subStoreOrderMap || null == mapByMainEntrustedOrder){
                throw new CustomBusinessException("订单信息异常,核销失败");
            }
            String labelPath = "";
            List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>(subStoreOrderMap.size());
            for (Map.Entry<String, TmsStoreOrderEntity> entityEntry : subStoreOrderMap.entrySet()) {
                TmsStoreOrderEntity storeOrderEntity = entityEntry.getValue();
                String subEntrustedOrderNumber = storeOrderEntity.getEntrustedOrderNumber();
                TmsStoreOrderGoodsEntity tmsStoreOrderGoodsEntity = mapByMainEntrustedOrder.get(subEntrustedOrderNumber);
                if(null == tmsStoreOrderGoodsEntity){
                    continue;
                }
                PackageOrderDataItem packageOrderDataItem = pushOrderToPacket(storeOrderEntity, tmsStoreOrderGoodsEntity, providerCode, subEntrustedOrderNumber);
                if(null == packageOrderDataItem){
                    throw new CustomBusinessException("推送小包系统订单失败,核销失败");
                }
                String trackingNo = packageOrderDataItem.getTrackingNo();
                labelPath = getPackageLabelPath(packageOrderDataItem);
                TmsStoreOrderEntity updateBean = new TmsStoreOrderEntity();
                updateBean.setId(storeOrderEntity.getId());
                updateBean.setStoreId(storeId);
                updateBean.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                updateBean.setPushTime(pushTime);
                updateBean.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
                updateBean.setExternalType(StoreEnums.StoreOrder.ExternalType.PACKET.getValue());
                updateBean.setWriteOffStoreId(storeId);
                updateBean.setExternalOrderNumber(trackingNo);
                baseMapper.updateById(updateBean);

                TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
                traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getEName());
                traceEntity.setSubEntrustedOrder(storeOrderEntity.getEntrustedOrderNumber());
                traceEntity.setMainEntrustedOrder(storeOrderEntity.getMainEntrustedOrder());
                storeOrderTraces.add(traceEntity);
            }
            TmsStoreOrderEntity mainStoreOrder = new TmsStoreOrderEntity();
            mainStoreOrder.setId(id);
            mainStoreOrder.setStoreId(storeId);
            mainStoreOrder.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue());
            mainStoreOrder.setPushTime(pushTime);
            mainStoreOrder.setWriteOffFlag(StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue());
            mainStoreOrder.setExternalType(StoreEnums.StoreOrder.ExternalType.PACKET.getValue());
            mainStoreOrder.setWriteOffStoreId(storeId);
            mainStoreOrder.setLabelPath(labelPath);
            baseMapper.updateById(mainStoreOrder);
            // 门店已经取货
            tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        }
        return true;
    }

    @Override
    public List<TmsStoreOrderEntity> selectSubStoreOrderByPushTime(List<Integer> orderStatus, LocalDateTime pushTime) {
        if(CollUtil.isEmpty(orderStatus) || null == pushTime){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TmsStoreOrderEntity::getMainEntrustedOrder);
        queryWrapper.isNotNull(TmsStoreOrderEntity::getExternalOrderNumber);
        queryWrapper.eq(TmsStoreOrderEntity::getSubFlag,StoreEnums.StoreOrder.SubFlag.SUB.getValue());
        queryWrapper.in(TmsStoreOrderEntity::getOrderStatus,orderStatus);
        queryWrapper.ge(TmsStoreOrderEntity::getPushTime,pushTime);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatusByEntrustedOrderNumber(String entrustedOrderNumber,Integer newOrderStatus) {
        if(StrUtil.isBlank(entrustedOrderNumber)){
            return;
        }
        LambdaUpdateWrapper<TmsStoreOrderEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(TmsStoreOrderEntity::getEntrustedOrderNumber,entrustedOrderNumber);
        queryWrapper.set(TmsStoreOrderEntity::getOrderStatus,newOrderStatus);
        baseMapper.update(queryWrapper);
    }

    @Override
    public ChannelPriceVO getImportOrderChannelPrice(List<ChannelPriceQueryDTO> channelPriceQueryDTOList, String channelCode) {
        if (CollUtil.isEmpty(channelPriceQueryDTOList)) {
            return new ChannelPriceVO();
        }

        // 汇总结果
        ChannelPriceVO totalResult = new ChannelPriceVO();
        totalResult.setProviderCode(channelCode);
        BigDecimal totalFreightAmount = BigDecimal.ZERO;

        // 按收发地邮编分组，key格式：发货邮编-收货邮编
        Map<String, List<ChannelPriceQueryDTO>> groupedOrders = channelPriceQueryDTOList.stream()
                .filter(dto -> dto != null && StrUtil.isNotBlank(dto.getShipperPostalCode()) && StrUtil.isNotBlank(dto.getReceiverPostalCode()))
                .collect(Collectors.groupingBy(dto -> dto.getShipperPostalCode() + "-" + dto.getReceiverPostalCode()));

        // 对每个分组进行询价
        for (Map.Entry<String, List<ChannelPriceQueryDTO>> entry : groupedOrders.entrySet()) {
            try {
                String[] postalCodes = entry.getKey().split("-");
                String shipperPostalCode = postalCodes[0];
                String receiverPostalCode = postalCodes[1];
                List<ChannelPriceQueryDTO> sameRouteOrders = entry.getValue();

                // 汇总该分组内所有订单的货物信息
                BigDecimal totalLength = BigDecimal.ZERO;
                BigDecimal totalWidth = BigDecimal.ZERO;
                BigDecimal totalHeight = BigDecimal.ZERO;
                BigDecimal totalWeight = BigDecimal.ZERO;

                for (ChannelPriceQueryDTO orderDTO : sameRouteOrders) {
                    List<StoreOrderGoodsDTO> storeOrderGoods = orderDTO.getStoreOrderGoods();
                    if (CollUtil.isEmpty(storeOrderGoods)) {
                        continue;
                    }

                    // 计算该订单的尺寸和重量
                    BigDecimal orderLength = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getLength)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderWidth = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getWidth)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderHeight = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getHeight)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal orderWeight = storeOrderGoods.stream()
                            .map(StoreOrderGoodsDTO::getWeight)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 累加到分组总量
                    totalLength = totalLength.add(orderLength);
                    totalWidth = totalWidth.add(orderWidth);
                    totalHeight = totalHeight.add(orderHeight);
                    totalWeight = totalWeight.add(orderWeight);
                }

                // 对该分组进行一次询价
                ChannelPriceVO groupResult = calculatePrice(
                    totalLength, totalWidth, totalHeight, totalWeight,
                    receiverPostalCode, shipperPostalCode, channelCode
                );

                // 汇总价格
                if (groupResult != null && groupResult.getFreightAmount() != null) {
                    totalFreightAmount = totalFreightAmount.add(groupResult.getFreightAmount());
                }

                logger.info("分组询价完成，路线: {}，订单数量: {}，分组运费: {}",
                    entry.getKey(), sameRouteOrders.size(),
                    groupResult != null ? groupResult.getFreightAmount() : "询价失败");

            } catch (Exception e) {
                logger.error("分组询价异常，路线: {}, 渠道: {}", entry.getKey(), channelCode, e);
            }
        }

        totalResult.setFreightAmount(totalFreightAmount);
        logger.info("批量询价完成，渠道: {}，总分组数: {}，总运费: {}",
            channelCode, groupedOrders.size(), totalFreightAmount);

        return totalResult;
    }

    @Override
    public R getStoreOrderByOrderNumber(String orderNumber) {
        if (StrUtil.isBlank(orderNumber) || orderNumber.length() < 13){
            return LocalizedR.failed("tms.store.order.not.exist","");
        }
        // 判断子单还是主单
        if(orderNumber.length() != 13){
            orderNumber = orderNumber.substring(0, 13);
        }
        TmsStoreOrderEntity storeOrder = baseMapper.selectOne(Wrappers.<TmsStoreOrderEntity>lambdaQuery()
                .eq(TmsStoreOrderEntity::getEntrustedOrderNumber, orderNumber));

        // 判断订单是否存在
        if (storeOrder == null) {
            return LocalizedR.failed("tms.store.order.not.exist", "");
        }

        // 如果订单存在，则判断其核销状态
        if (StoreEnums.StoreOrder.WriteOffFlag.WriteOff.getValue().equals(storeOrder.getWriteOffFlag())) {
            // 订单已核销
            return LocalizedR.failed("tms.store.order.already.writeoff", "");
        }
        // 基本信息
        StoreOrderDetailVO detailVO = BeanUtil.toBean(storeOrder, StoreOrderDetailVO.class);
        // 基础费用
        TmsStoreProviderRelationEntity entity = storeProviderRelationService.getByMainEntrustedOrder(orderNumber);
        if(null != entity){
            StoreProviderRelationVO providerRelationVO = BeanUtil.toBean(entity, StoreProviderRelationVO.class);
            detailVO.setStoreProviderRelationVO(providerRelationVO);
        }
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = storeOrderGoodsService.getByMainEntrustedOrder(orderNumber);
        if(CollUtil.isNotEmpty(storeOrderGoods)){
            List<StoreOrderGoodsVO> goodsVOS = BeanUtil.copyToList(storeOrderGoods, StoreOrderGoodsVO.class);
            detailVO.setStoreOrderGoods(goodsVOS);
        }
        // 异常信息
        List<TmsStoreOrderExceptionEntity> storeOrderExceptions = tmsStoreOrderExceptionService.getStoreOrderExceptionsByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderExceptions)){
            List<TmsStoreOrderExceptionVo> exceptionVOS = BeanUtil.copyToList(storeOrderExceptions, TmsStoreOrderExceptionVo.class);
            detailVO.setExceptionList(exceptionVOS);
        }
        // 包裹赔付信息
        List<TmsStoreOrderPkgPayEntity> storeOrderPkgPays = tmsStoreOrderPkgPayService.getStoreOrderPkgPaysByStoreOrderId(storeOrder.getId());
        if(CollUtil.isNotEmpty(storeOrderPkgPays)){
            List<TmsStoreOrderPkgPayVo> pkgPayVOS = BeanUtil.copyToList(storeOrderPkgPays, TmsStoreOrderPkgPayVo.class);
            detailVO.setPkgPayList(pkgPayVOS);
        }
        // 客户信息
        StoreCustomerVO customerInfo = storeCustomerService.getStoreCustomerByStoreId(storeOrder.getStoreCustomerId());
        if (customerInfo != null){
            detailVO.setCustomerInfo(customerInfo);
        }
        return R.ok(detailVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveStoreOrderBatch(List<StoreOrderDTO> storeOrderDTOs) {
        if (storeOrderDTOs == null || storeOrderDTOs.isEmpty()) {
            throw new CustomBusinessException("批量下单列表为空");
        }

        // 客户唯一，只校验一次
        StoreOrderDTO firstOrder = storeOrderDTOs.get(0);
        Long storeCustomerId = firstOrder.getStoreCustomerId();
        TmsStoreCustomerEntity storeCustomer;
        if (storeCustomerId == null) {
            Long userId = SecurityUtils.getUser().getId();
            storeCustomer = storeCustomerService.getStoreCustomerByUserId(userId);
            if (storeCustomer == null) {
                throw new CustomBusinessException("客户不存在,不支持下单");
            }
        } else {
            storeCustomer = storeCustomerService.getStoreCustomerById(storeCustomerId);
            if (storeCustomer == null) {
                throw new CustomBusinessException("客户不存在,不支持下单");
            }
        }

        // 校验重量是否超过68KG
        boolean hasOverweightGoods = storeOrderDTOs.stream()
                .flatMap(order -> order.getStoreOrderGoods().stream())
                .anyMatch(goods -> goods.getWeight() != null &&
                        goods.getWeight().compareTo(BigDecimal.valueOf(68)) > 0);
        if (hasOverweightGoods) {
            throw new CustomBusinessException("货物重量超过68KG，不支持下单");
        }

        // 主体数据准备
        List<TmsStoreOrderEntity> saveStoreOrders = new ArrayList<>();
        List<TmsStoreOrderGoodsEntity> storeOrderGoods = new ArrayList<>();
        List<TmsStoreOrderTraceEntity> storeOrderTraces = new ArrayList<>();
        List<TmsStoreProviderRelationEntity> storeProviderRelations = new ArrayList<>();
        List<String> businessKeys = new ArrayList<>();
        // 用于主单号递增计数
        Long orderCount = baseMapper.getSubOrderCount();
        int orderCounts = orderCount != null ? orderCount.intValue() : 0;

        for (StoreOrderDTO storeOrderDTO : storeOrderDTOs) {
            // 货物校验
            List<StoreOrderGoodsDTO> orderGoodsDTOS = storeOrderDTO.getStoreOrderGoods();
            if (CollUtil.isEmpty(orderGoodsDTOS)) {
                throw new CustomBusinessException("货物信息为空，不支持下单");
            }

            // 注意：必须每单调用一次主单号工具
            String mainOrderNo = ExpressOrderNoUtil.generateMainOrderNo(orderCounts++);

            // 主订单
            TmsStoreOrderEntity mainStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
            mainStoreOrder.setEntrustedOrderNumber(mainOrderNo);
            mainStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.MAIN.getValue());
            saveStoreOrders.add(mainStoreOrder);


            // 子单与goods、trace
            AtomicInteger index = new AtomicInteger(0);
            for (StoreOrderGoodsDTO goodsDTO : orderGoodsDTOS) {
                TmsStoreOrderEntity subStoreOrder = BeanUtil.toBean(storeOrderDTO, TmsStoreOrderEntity.class);
                String subOrderNo = ExpressOrderNoUtil.generateSubOrderNo(mainOrderNo, index.incrementAndGet());
                subStoreOrder.setEntrustedOrderNumber(subOrderNo);
                subStoreOrder.setMainEntrustedOrder(mainOrderNo);
                subStoreOrder.setSubFlag(StoreEnums.StoreOrder.SubFlag.SUB.getValue());
                saveStoreOrders.add(subStoreOrder);
                businessKeys.add(subOrderNo);

                TmsStoreOrderGoodsEntity storeOrderGood = BeanUtil.toBean(goodsDTO, TmsStoreOrderGoodsEntity.class);
                storeOrderGood.setUnitType(storeOrderDTO.getUnitType());
                storeOrderGood.setSubEntrustedOrder(subOrderNo);
                storeOrderGood.setMainEntrustedOrder(mainOrderNo);
                storeOrderGoods.add(storeOrderGood);

                TmsStoreOrderTraceEntity traceEntity = new TmsStoreOrderTraceEntity();
                traceEntity.setOrderStatus(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getValue());
                traceEntity.setOrderStatusContext(StoreEnums.StoreOrder.OrderStatus.AWAITING_SHIPMENT.getEName());
                traceEntity.setSubEntrustedOrder(subOrderNo);
                traceEntity.setMainEntrustedOrder(mainOrderNo);
                storeOrderTraces.add(traceEntity);
            }

            // 服务商
            StoreProviderRelationVO providerRelation = storeOrderDTO.getStoreProviderRelationVO();
            TmsStoreProviderRelationEntity saveBean = BeanUtil.toBean(providerRelation, TmsStoreProviderRelationEntity.class);
            saveBean.setMainEntrustedOrder(mainOrderNo);
            storeProviderRelations.add(saveBean);
        }

        // 批量写入
        baseMapper.insert(saveStoreOrders);
        storeOrderGoodsService.saveStoreOrderGoods(storeOrderGoods);
        tmsStoreOrderTraceService.saveStoreOrderTrace(storeOrderTraces);
        storeProviderRelationService.saveStoreOrderProviderBatch(storeProviderRelations);
        if(CollUtil.isNotEmpty(businessKeys)){
            // 站内消息推送
            storeMessageTraceService.saveStoreMessageTrace(storeCustomerId,StoreEnums.StoreMessageTrace.BusinessSubType.ORDER_SUCCESS.getValue(),businessKeys);
        }
        return true;
    }

    @Override
    public String getOrderWaybill(Long id) {
        TmsStoreOrderEntity tmsStoreOrder = getById(id);
//        if (tmsStoreOrder != null) {
//            return tmsStoreOrder.getWaybillNumber();
//        }
        return "";
    }

    @Override
    public List<StoreOrderListExcelVO> exportStoreOrderList(StoreOrderQueryDTO storeOrderQueryDTO) {
        storeOrderQueryDTO.setSize(Integer.MAX_VALUE);
        IPage<StoreOrderVO> storeOrderVOPage = this.getTmsStoreOrderStorePage(storeOrderQueryDTO);
        if(null == storeOrderVOPage){
            return new ArrayList<>();
        }
        List<StoreOrderListExcelVO> storeOrderExcelVOS = new ArrayList<>();
        for (StoreOrderVO record : storeOrderVOPage.getRecords()) {
            StoreOrderListExcelVO exportBean = BeanUtil.toBean(record, StoreOrderListExcelVO.class);
            storeOrderExcelVOS.add(exportBean);
        }
        return storeOrderExcelVOS;
    }

    @Override
    public List<StoreOrderAdminExcelVO> exportStoreOrderAdmin(StoreOrderQueryDTO storeOrderQueryDTO) {
        storeOrderQueryDTO.setSize(Integer.MAX_VALUE);
        IPage<StoreOrderVO> storeOrderVOPage = this.getTmsStoreOrderAdminPage(storeOrderQueryDTO);
        if(null == storeOrderVOPage){
            return new ArrayList<>();
        }
        List<StoreOrderAdminExcelVO> storeOrderExcelVOS = new ArrayList<>();
        for (StoreOrderVO record : storeOrderVOPage.getRecords()) {
            StoreOrderAdminExcelVO exportBean = BeanUtil.toBean(record, StoreOrderAdminExcelVO.class);
            storeOrderExcelVOS.add(exportBean);
        }
        return storeOrderExcelVOS;
    }

    private String getPackageLabelPath(PackageOrderDataItem packageOrderDataItem) {
        String labelPath = packageOrderDataItem.getLabelPath();
        if (StrUtil.isBlank(labelPath)) {
            List<PackageOrderResults> packageResults = packageOrderDataItem.getPackageResults();

            if (CollUtil.isNotEmpty(packageResults)) {
                labelPath = packageResults.get(0).getLabelPath();
            }
        }
        return labelPath;
    }

    /**
     * 获取当前用户的门店ID
     * @return
     */
    private Long getCurrentUserStoreId() {
        Long userId = SecurityUtils.getUser().getId();
        TmsStoreUserEntity storeUser = storeUserService.getOne(new LambdaQueryWrapper<TmsStoreUserEntity>().eq(TmsStoreUserEntity::getUserId, userId));
        if (storeUser == null){
            return null;
        }
        return storeUser.getStoreId();
    }

    /**
     * 导入订单询价
     */
    public ChannelPriceVO calculatePrice(BigDecimal totalLength, BigDecimal totalWidth, BigDecimal totalHeight,
                                         BigDecimal totalWeight, String receiverPostalCode, String shipperPostalCode,
                                         String channelCode) {

        ChannelPriceVO channelPriceVO = new ChannelPriceVO();
        channelPriceVO.setProviderCode(channelCode);

        try {
            // NB服务商询价
            if (StoreConstants.NB_SERVICE_PROVIDER.equals(channelCode)) {
                BigDecimal totalVolume = totalLength.multiply(totalWidth).multiply(totalHeight)
                        .divide(StoreConstants.VOLUME_CM3_CONVERT_M3);

                TmsOrderPriceCalculationVo order = new TmsOrderPriceCalculationVo();
                order.setDestPostalCode(receiverPostalCode);
                order.setShipperPostalCode(shipperPostalCode);
                order.setTotalWeight(totalWeight);
                order.setTotalVolume(totalVolume);

                PriceCalculationRequestVo priceCalculationRequestVo = new PriceCalculationRequestVo();
                priceCalculationRequestVo.setOrders(Collections.singletonList(order));
                priceCalculationRequestVo.setProviderName(channelCode);

                PriceCalculationResultVo priceCalculationResultVo = tmsReceivableService.calculatePrice(priceCalculationRequestVo);
                if (priceCalculationResultVo != null) {
                    channelPriceVO.setFreightAmount(priceCalculationResultVo.getTotalPrice());
                }
            }
            // packet服务商询价（CA开头的渠道）
            else if (channelCode.startsWith("CA")) {
                FeeRequest feeRequest = new FeeRequest();
                feeRequest.setChannelCode(Collections.singletonList(channelCode));
                feeRequest.setLength(totalLength);
                feeRequest.setWidth(totalWidth);
                feeRequest.setHeight(totalHeight);
                feeRequest.setWeight(totalWeight);
                feeRequest.setPostCode(receiverPostalCode);
                feeRequest.setIso2(StoreConstants.CA);

                ResponseResult<FreightRoot> freightRootResponseResult = postBusinessUtils.calculateFreight(feeRequest);
                if (freightRootResponseResult != null && freightRootResponseResult.getData() != null) {
                    FreightRoot freightRoot = freightRootResponseResult.getData();
                    if (freightRoot != null && CollUtil.isNotEmpty(freightRoot.getData())) {
                        List<FreightDataItem> data = freightRoot.getData();
                        for (FreightDataItem item : data) {
                            if (channelCode.equals(item.getChannelCode())) {
                                String totalFee = item.getTotalFee();
                                if (StrUtil.isNotBlank(totalFee)) {
                                    channelPriceVO.setFreightAmount(BigDecimal.valueOf(Double.parseDouble(totalFee)));
                                }
                                break;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("询价异常，渠道: {}", channelCode, e);
        }

        return channelPriceVO;
    }

    /**
     * 校验单个字段是否为空
     */
    private void validateField(String fieldValue, String fieldName, int rowIndex, List<String> errorMessages) {
        if (fieldValue == null || fieldValue.trim().isEmpty()) {
            errorMessages.add("第【" + rowIndex + "】行：" + fieldName + "不能为空");
        }
    }


}
