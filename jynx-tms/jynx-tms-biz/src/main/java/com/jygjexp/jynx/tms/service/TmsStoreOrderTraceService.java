package com.jygjexp.jynx.tms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jygjexp.jynx.tms.dto.StoreTraceNumberDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderTraceEntity;
import com.jygjexp.jynx.tms.vo.StoreOrderTracesVO;

import java.util.List;

public interface TmsStoreOrderTraceService extends IService<TmsStoreOrderTraceEntity> {
    /**
     *
     * @param traceNumberDTO
     * @return
     */
    StoreOrderTracesVO getStoreOrderTraces(StoreTraceNumberDTO traceNumberDTO);
    /**
     * 存储订单踪迹记录
     * @param storeOrderTraces
     */
    void saveStoreOrderTrace(List<TmsStoreOrderTraceEntity> storeOrderTraces);
    /**
     * 通过子单获取所有轨迹
     * @param subEntrustedOrder
     * @return
     */
    List<TmsStoreOrderTraceEntity> selectAllTraceBySubEntrustedOrder(String subEntrustedOrder);
    /**
     * 通过子单获取所有轨迹
     * @param subEntrustedOrder
     * @return
     */
    TmsStoreOrderTraceEntity getTraceMaxSycTime(String subEntrustedOrder);

}
