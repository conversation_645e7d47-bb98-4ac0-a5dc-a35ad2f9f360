package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.excel.annotation.ResponseExcel;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.common.security.annotation.Inner;
import com.jygjexp.jynx.tms.dto.TmsSortingRecordDto;
import com.jygjexp.jynx.tms.dto.TmsSortingRecordPageDto;
import com.jygjexp.jynx.tms.entity.TmsCargoInfoEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingRecordEntity;
import com.jygjexp.jynx.tms.entity.TmsSortingTemplateEntity;
import com.jygjexp.jynx.tms.service.TmsCargoInfoService;
import com.jygjexp.jynx.tms.service.TmsSortingRecordService;
import com.jygjexp.jynx.tms.service.TmsSortingTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 分拣记录表
 *
 * <AUTHOR>
 * @date 2025-06-25 11:36:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsSortingRecord" )
@Tag(description = "tmsSortingRecord" , name = "分拣记录表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsSortingRecordController {

    private final  TmsSortingRecordService tmsSortingRecordService;
    private final TmsSortingTemplateService tmsSortingTemplateService;
    private final TmsCargoInfoService tmsCargoInfoService;

    /**
     * 分页查询
     * @param tmsSortingRecordPageDto 分拣查询条件
     * @return
     */
    @Inner(value = false)
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_view')" )
    public R getTmsSortingRecordPage(@RequestBody TmsSortingRecordPageDto tmsSortingRecordPageDto) {
        Page page = new Page(tmsSortingRecordPageDto.getCurrent(), tmsSortingRecordPageDto.getSize());
        TmsSortingRecordDto tmsSortingRecordDto = tmsSortingRecordPageDto.getTmsSortingRecordDto();
        return tmsSortingRecordService.pageSearch(page,tmsSortingRecordDto);
    }


    /**
     * 获取所有分拣模版名称
     * @return R
     */
    @Operation(summary = "获取所有分拣模版名称" , description = "获取所有分拣模版名称" )
    @GetMapping("/getAllSortingTemplate" )
    public R getAllSortingTemplate() {
        LambdaQueryWrapper<TmsSortingTemplateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TmsSortingTemplateEntity::getTemplateName);
        return R.ok(tmsSortingTemplateService.list(wrapper));
    }

    /**
     * 通过id查询分拣记录表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(tmsSortingRecordService.getById(id));
    }

    /**
     * 新增分拣记录表
     * @param tmsSortingRecord 分拣记录表
     * @return R
     */
    @Inner(value = false)
    @Operation(summary = "新增分拣记录表" , description = "新增分拣记录表" )
    @SysLog("新增分拣记录表" )
    @PostMapping("/add")
//    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_add')" )
    public R save(@RequestBody TmsSortingRecordEntity tmsSortingRecord) {
        // 添加订单复核重量
        if (Objects.nonNull(tmsSortingRecord.getOrderNo())) {
            // TODO 半托管的单兼容- 目前系统不存在
            TmsCargoInfoEntity cargoInfoEntity = tmsCargoInfoService.getByEntrustedOrderNumber(tmsSortingRecord.getOrderNo());
            if(null != cargoInfoEntity && null != tmsSortingRecord.getPackageWeight()){
                TmsCargoInfoEntity updateEntity = new TmsCargoInfoEntity();
                updateEntity.setId(cargoInfoEntity.getId());
                updateEntity.setReviewWeight(tmsSortingRecord.getPackageWeight());
                tmsCargoInfoService.updateById(updateEntity);
            }
        }
        return R.ok(tmsSortingRecordService.save(tmsSortingRecord));
    }

    /**
     * 修改分拣记录表
     * @param tmsSortingRecord 分拣记录表
     * @return R
     */
    @Operation(summary = "修改分拣记录表" , description = "修改分拣记录表" )
    @SysLog("修改分拣记录表" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_edit')" )
    public R updateById(@RequestBody TmsSortingRecordEntity tmsSortingRecord) {
        // 添加订单复核重量
        if (Objects.nonNull(tmsSortingRecord.getOrderNo())) {
            tmsCargoInfoService.update(Wrappers.<TmsCargoInfoEntity>lambdaUpdate()
                    .set((tmsSortingRecord.getPackageWeight() != null), TmsCargoInfoEntity::getReviewWeight, tmsSortingRecord.getPackageWeight())
                    .eq(TmsCargoInfoEntity::getEntrustedOrderNumber, tmsSortingRecord.getOrderNo()));
        }
        return R.ok(tmsSortingRecordService.updateById(tmsSortingRecord));
    }

    /**
     * 通过id删除分拣记录表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除分拣记录表" , description = "通过id删除分拣记录表" )
    @SysLog("通过id删除分拣记录表" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(tmsSortingRecordService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 分拣日志导出
     */
    @ResponseExcel
    @Operation(summary = "分拣日志导出" , description = "分拣日志导出" )
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('tms_tmsSortingRecord_export')" )
    public List<? extends Object> export(@RequestBody TmsSortingRecordPageDto tmsSortingRecordPageDto) {
        return tmsSortingRecordService.export(tmsSortingRecordPageDto.getTmsSortingRecordDto());
    }
}
