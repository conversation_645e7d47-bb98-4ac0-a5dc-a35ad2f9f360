package com.jygjexp.jynx.tms.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.constants.StoreEnums;
import com.jygjexp.jynx.tms.entity.TmsStoreBalanceEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity;
import com.jygjexp.jynx.tms.entity.TmsStoreOrderPkgPayEntity;
import com.jygjexp.jynx.tms.enums.NewOrderStatus;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper;
import com.jygjexp.jynx.tms.mapper.TmsStoreOrderPkgPayMapper;
import com.jygjexp.jynx.tms.response.LocalizedR;
import com.jygjexp.jynx.tms.service.TmsStoreBalanceService;
import com.jygjexp.jynx.tms.service.TmsStoreOrderPkgPayService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 快递订单包裹赔付表
 *
 * <AUTHOR>
 * @date 2025-07-21 11:48:01
 */
@Service
@RequiredArgsConstructor
public class TmsStoreOrderPkgPayServiceImpl extends ServiceImpl<TmsStoreOrderPkgPayMapper, TmsStoreOrderPkgPayEntity> implements TmsStoreOrderPkgPayService {
    private final TmsStoreOrderMapper tmsStoreOrderMapper;
    private final TmsStoreBalanceService tmsStoreBalanceService;

    @Override
    public List<TmsStoreOrderPkgPayEntity> getStoreOrderPkgPaysByStoreOrderId(Long id) {
        return this.baseMapper.selectList(Wrappers.<TmsStoreOrderPkgPayEntity>lambdaQuery().eq(TmsStoreOrderPkgPayEntity::getOrderId, id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R savePkgPay(TmsStoreOrderPkgPayEntity tmsStoreOrderPkgPay) {
        // 查询订单信息
        TmsStoreOrderEntity tmsStoreOrder = tmsStoreOrderMapper.selectById(tmsStoreOrderPkgPay.getOrderId());
        if (tmsStoreOrder == null) {
            return LocalizedR.failed("tms.store.order.not.exist", "");
        }

        // 判断订单状态是否可以进行赔付
        if (!(tmsStoreOrder.getOrderStatus() >= StoreEnums.StoreOrder.OrderStatus.STORE_PICKUP.getValue())) {
            return LocalizedR.failed("tms.store.order.status.invalid", "");
        }

        // 校验订单运费
        BigDecimal totalFreightAmount = tmsStoreOrder.getTotalFreightAmount();
        if (totalFreightAmount == null) {
            return LocalizedR.failed("tms.store.order.lack.amount", "");
        }

        // 校验赔付金额合法
        BigDecimal compensationAmount = tmsStoreOrderPkgPay.getCompensationAmount();
        if (compensationAmount == null || compensationAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return LocalizedR.failed("tms.store.order.pkg.comp.amount.invalid", "");
        }

        // 校验赔付金额不超订单运费
        if (compensationAmount.compareTo(totalFreightAmount) > 0) {
            return LocalizedR.failed("tms.store.order.pkg.beyond.amount", "");
        }

        // 查询客户余额
        TmsStoreBalanceEntity tmsStoreBalance = tmsStoreBalanceService.getByStoreCustomerId(tmsStoreOrder.getStoreCustomerId());
        if (tmsStoreBalance == null || tmsStoreBalance.getAmount() == null) {
            return LocalizedR.failed("tms.store.order.pkg.no.balance.account", "");
        }

        // 校验余额是否充足
        if (tmsStoreBalance.getAmount().compareTo(compensationAmount) < 0) {
            return LocalizedR.failed("tms.store.order.pkg.beyond.balance", "");
        }

        // 赔付并增加余额
        Boolean compensate = tmsStoreBalanceService.compensate(
                tmsStoreOrder.getStoreCustomerId(), compensationAmount);
        if (!compensate) {
            return LocalizedR.failed("tms.store.order.pkg.compensate.failed", "");
        }

        // 保存赔付记录
        boolean paySaved = save(tmsStoreOrderPkgPay);
        return paySaved ? R.ok() : LocalizedR.failed("tms.store.order.pkg.pay.save.failed", "");
    }

}