package com.jygjexp.jynx.tms.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.common.log.annotation.SysLog;
import com.jygjexp.jynx.tms.dto.StoreCreditCardsDTO;
import com.jygjexp.jynx.tms.entity.TmsStoreCreditCardEntity;
import com.jygjexp.jynx.tms.service.TmsStoreCreditCardService;
import com.jygjexp.jynx.tms.vo.StoreCreditCardVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 门店客户信用卡表
 *
 * <AUTHOR>
 * @date 2025-07-14 17:49:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tmsStoreCreditCard" )
@Tag(description = "tmsStoreCreditCard" , name = "门店客户信用卡表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class TmsStoreCreditCardController {

    private final TmsStoreCreditCardService service;

    /**
     * 分页查询
     * @param page 分页对象
     * @param isShow 是否明文展示
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_view')" )
    public R selectPage(@ParameterObject Page page, @RequestParam("isShow") Integer isShow) {
        return R.ok(service.selectPage(page, isShow));
    }

    /**
     * 新增客户信用卡表
     * @param storeCreditCardsDTO 客户信用卡表
     * @return R
     */
    @Operation(summary = "新增信用卡表" , description = "新增信用卡表" )
    @SysLog("creditCards-新增" )
    @PostMapping("/save")
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_add')" )
    public R saveCreditCard(@RequestBody @Valid StoreCreditCardsDTO storeCreditCardsDTO) {
        boolean result = service.saveCreditCard(storeCreditCardsDTO);
        return result ? R.ok() : R.failed();
    }
    /**
     * 通过id查询门店客户信用卡表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        TmsStoreCreditCardEntity creditCardEntity = service.getById(id);
        StoreCreditCardVO creditCardVO = BeanUtil.toBean(creditCardEntity, StoreCreditCardVO.class);
        return R.ok(creditCardVO);
    }
    /**
     * 修改客户信用卡表
     * @param storeCreditCardsDTO 客户信用卡表
     * @return R
     */
    @Operation(summary = "修改客户信用卡表" , description = "修改客户信用卡表" )
    @SysLog("creditCards-修改" )
    @PutMapping("/update")
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_update')" )
    public R updateCreditCard(@RequestBody @Valid StoreCreditCardsDTO storeCreditCardsDTO) {
        return R.ok(service.updateCreditCard(storeCreditCardsDTO));
    }

    /**
     * 通过id删除客户信用卡表
     * @param id id列表
     * @return R
     */
    @Operation(summary = "通过id删除客户信用卡表" , description = "通过id删除客户信用卡表" )
    @SysLog("creditCards-删除" )
    @DeleteMapping("/delete")
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_delete')" )
    public R removeById(@RequestParam(value = "id",required = true) Long id) {
        return R.ok(service.removeById(id));
    }


    @Operation(summary = "通过id设置客户信用卡默认" , description = "通过id设置客户信用卡默认" )
    @PutMapping("/default_fag")
    @PreAuthorize("@pms.hasPermission('tms_storeCreditCard_default')" )
    public R setDefaultFlag(@RequestParam(value = "id",required = true) Long id,
                            @RequestParam(value = "defaultFlag",required = true) Integer defaultFlag) {
        return R.ok(service.setDefaultFlag(id,defaultFlag));
    }
}
