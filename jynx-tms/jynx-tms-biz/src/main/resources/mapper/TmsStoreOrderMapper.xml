<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jygjexp.jynx.tms.mapper.TmsStoreOrderMapper">

    <resultMap id="tmsStoreOrderMap" type="com.jygjexp.jynx.tms.entity.TmsStoreOrderEntity">
        <id property="id" column="id"/>
        <result property="entrustedOrderNumber" column="entrusted_order_number"/>
        <result property="storeCustomerId" column="store_customer_id"/>
        <result property="totalFreightAmount" column="total_freight_amount"/>
        <result property="orderStatus" column="order_status"/>
        <result property="podSign" column="pod_sign"/>
        <result property="insuranceSign" column="insurance_sign"/>
        <result property="batterySign" column="battery_sign"/>
        <result property="printStatus" column="print_status"/>
        <result property="printTime" column="print_time"/>
        <result property="sendType" column="send_type"/>
        <result property="shipperName" column="shipper_name"/>
        <result property="shipperPhone" column="shipper_phone"/>
        <result property="shipperOrigin" column="shipper_origin"/>
        <result property="shipperPostalCode" column="shipper_postal_code"/>
        <result property="shipperAddress" column="shipper_address"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="receiverDest" column="receiver_dest"/>
        <result property="receiverPostalCode" column="receiver_postal_code"/>
        <result property="receiverAddress" column="receiver_address"/>
        <result property="revision" column="revision"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="getTmsStoreOrderStorePage" resultType="com.jygjexp.jynx.tms.vo.StoreOrderVO">
        SELECT
            o.id AS id,
            o.store_customer_id AS storeCustomerId,
            o.entrusted_order_number AS entrustedOrderNumber,
            o.external_order_number AS externalOrderNumber,
            o.order_status AS orderStatus,
            o.shipper_origin AS shipperOrigin,
            o.receiver_dest AS receiverDest,
            o.shipper_name AS shipperName,
            o.shipper_phone AS shipperPhone,
            o.shipper_address AS shipperAddress,
            o.receiver_name AS receiverName,
            o.receiver_phone AS receiverPhone,
            o.receiver_address AS receiverAddress,
            o.total_freight_amount AS totalFreightAmount,
            o.pod_sign AS podSign,
            o.insurance_sign AS insuranceSign,
            o.battery_sign AS batterySign,
            o.remark AS remark,
            o.create_time AS createTime,
            o.print_status AS printStatus,
            o.print_time AS printTime,
            o.label_path AS labelPath,
            o.external_type AS externalType,
            c.name AS customerName,
            COALESCE(r.provider_name, '') AS providerName,
            COALESCE(r.provider_transport_time, '') AS providerTransportTime,
            COALESCE(g.totalCounts, 0) AS totalCounts,
            COALESCE(g.totalWeight, 0) AS totalWeight,
            COALESCE(g.totalVolume, 0) AS totalVolume
        FROM tms_store_order o
        LEFT JOIN tms_store_customer c ON c.id = o.store_customer_id AND c.del_flag = '0'
        LEFT JOIN (
            SELECT
                main_entrusted_order,
                COUNT(id) AS totalCounts,
                SUM(weight) AS totalWeight,
                ROUND(SUM(length * width * height) / 1000000, 3) AS totalVolume
            FROM tms_store_order_goods
                WHERE del_flag = '0'
            GROUP BY main_entrusted_order
        ) g ON g.main_entrusted_order = o.entrusted_order_number
        LEFT JOIN (
            SELECT
                main_entrusted_order,
                MAX(provider_name) AS provider_name,
                MAX(provider_transport_time) AS provider_transport_time
            FROM tms_store_provider_relation
                WHERE del_flag = '0'
                <if test="storeOrderQueryDTO != null and storeOrderQueryDTO.providerCode != null and storeOrderQueryDTO.providerCode != ''">
                    AND provider_code = #{storeOrderQueryDTO.providerCode}
                </if>
            GROUP BY main_entrusted_order
        ) r ON r.main_entrusted_order = o.entrusted_order_number
        <where>
            (
            (o.write_off_flag = 0 AND o.store_id = #{storeId})
            OR
            (o.write_off_flag = 1 AND o.write_off_store_id = #{storeId})
            ) AND o.sub_flag = 0 AND o.del_flag = '0'
            <if test="storeOrderQueryDTO != null">
                <if test="storeOrderQueryDTO.entrustedOrderNumber != null and storeOrderQueryDTO.entrustedOrderNumber != ''">
                    AND o.entrusted_order_number like CONCAT('%', #{storeOrderQueryDTO.entrustedOrderNumber}, '%')
                </if>
                <if test="storeOrderQueryDTO.orderStatus != null">
                    AND o.order_status = #{storeOrderQueryDTO.orderStatus}
                </if>
                <if test="storeOrderQueryDTO.contacts != null and storeOrderQueryDTO.contacts != ''">
                    AND (o.shipper_name like CONCAT('%', #{storeOrderQueryDTO.contacts}, '%') OR o.receiver_name like CONCAT('%', #{storeOrderQueryDTO.contacts}, '%'))
                </if>
                <if test="storeOrderQueryDTO.phone != null and storeOrderQueryDTO.phone != ''">
                    AND (o.shipper_phone like CONCAT('%', #{storeOrderQueryDTO.phone}, '%') OR o.receiver_phone like CONCAT('%', #{storeOrderQueryDTO.phone}, '%'))
                </if>
                <if test="storeOrderQueryDTO.orderTimeStart != null and storeOrderQueryDTO.orderTimeEnd != null">
                    AND o.create_time &gt;= #{storeOrderQueryDTO.orderTimeStart}
                    AND o.create_time &lt;= #{storeOrderQueryDTO.orderTimeEnd}
                </if>
                <if test="storeOrderQueryDTO.storeCustomerId != null">
                    AND o.store_customer_id = #{storeOrderQueryDTO.storeCustomerId}
                </if>
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>

    <select id="getTmsStoreOrderAdminPage" resultType="com.jygjexp.jynx.tms.vo.StoreOrderVO">
        SELECT
            o.id AS id,
            o.store_customer_id AS storeCustomerId,
            o.entrusted_order_number AS entrustedOrderNumber,
            o.order_status AS orderStatus,
            o.shipper_origin AS shipperOrigin,
            o.receiver_dest AS receiverDest,
            o.shipper_name AS shipperName,
            o.shipper_phone AS shipperPhone,
            o.shipper_address AS shipperAddress,
            o.receiver_name AS receiverName,
            o.receiver_phone AS receiverPhone,
            o.receiver_address AS receiverAddress,
            o.total_freight_amount AS totalFreightAmount,
            r.base_freight_amount AS baseFreightAmount,
            (COALESCE(o.total_freight_amount,0) - COALESCE(r.base_freight_amount,0)) AS diffAmount,
            o.pod_sign AS podSign,
            o.insurance_sign AS insuranceSign,
            o.battery_sign AS batterySign,
            o.remark AS remark,
            o.create_time AS createTime,
            o.print_status AS printStatus,
            o.print_time AS printTime,
            o.write_off_flag AS writeOffFlag,
            c.name AS customerName,
            s.store_name AS storeName,
            COALESCE(r.provider_name, '') AS providerName,
            COALESCE(r.provider_transport_time, '') AS providerTransportTime,
            COALESCE(g.totalCounts, 0) AS totalCounts,
            COALESCE(g.totalWeight, 0) AS totalWeight,
            COALESCE(g.totalVolume, 0) AS totalVolume
        FROM tms_store_order o
        LEFT JOIN tms_store_customer c ON c.id = o.store_customer_id AND c.del_flag = '0'
        LEFT JOIN tms_store s ON s.id = o.store_id AND s.del_flag = '0'
        LEFT JOIN (
            SELECT
                main_entrusted_order,
                COUNT(id) AS totalCounts,
                SUM(weight) AS totalWeight,
                ROUND(SUM(length * width * height) / 1000000, 3) AS totalVolume
            FROM tms_store_order_goods
                WHERE del_flag = '0'
            GROUP BY main_entrusted_order
        ) g ON g.main_entrusted_order = o.entrusted_order_number
        LEFT JOIN (
            SELECT
                main_entrusted_order,
                MAX(provider_name) AS provider_name,
                MAX(provider_transport_time) AS provider_transport_time,
                MAX(base_freight_amount) AS base_freight_amount
            FROM tms_store_provider_relation
                WHERE del_flag = '0'
            GROUP BY main_entrusted_order
        ) r ON r.main_entrusted_order = o.entrusted_order_number
        <where>
            o.sub_flag = 0 AND o.del_flag = '0'
            <if test="storeOrderQueryDTO != null">
                <if test="storeOrderQueryDTO.entrustedOrderNumber != null and storeOrderQueryDTO.entrustedOrderNumber != ''">
                    AND o.entrusted_order_number like CONCAT('%', #{storeOrderQueryDTO.entrustedOrderNumber}, '%')
                </if>
                <if test="storeOrderQueryDTO.orderTimeStart != null and storeOrderQueryDTO.orderTimeEnd != null">
                    AND o.create_time &gt;= #{storeOrderQueryDTO.orderTimeStart}
                    AND o.create_time &lt;= #{storeOrderQueryDTO.orderTimeEnd}
                </if>
                <if test="storeOrderQueryDTO.storeCustomerId != null">
                    AND o.store_customer_id = #{storeOrderQueryDTO.storeCustomerId}
                </if>
                <if test="storeOrderQueryDTO.storeId != null">
                    AND o.store_id = #{storeOrderQueryDTO.storeId}
                </if>
            </if>
        </where>
        ORDER BY o.create_time DESC
    </select>


    <select id="getTotalFreightAmountSum" resultType="BigDecimal">
        SELECT
        COALESCE(SUM(a.total_freight_amount), 0.00) AS total_freight_amount
        FROM tms_store_order AS a
        WHERE a.sub_flag = 0 AND a.write_off_flag = 0
        AND a.store_customer_id = #{storeCustomerId}
    </select>


    <select id="selectPageWithJoin" resultType="com.jygjexp.jynx.tms.vo.StoreOrderVO">
        SELECT
            o.id AS id,
            o.store_customer_id AS storeCustomerId,
            o.entrusted_order_number AS entrustedOrderNumber,
            o.external_order_number AS externalOrderNumber,
            o.order_status AS orderStatus,
            o.shipper_origin AS shipperOrigin,
            o.receiver_dest AS receiverDest,
            o.shipper_name AS shipperName,
            o.shipper_phone AS shipperPhone,
            o.shipper_address AS shipperAddress,
            o.receiver_name AS receiverName,
            o.receiver_phone AS receiverPhone,
            o.receiver_address AS receiverAddress,
            o.total_freight_amount AS totalFreightAmount,
            o.pod_sign AS podSign,
            o.insurance_sign AS insuranceSign,
            o.battery_sign AS batterySign,
            o.remark AS remark,
            o.create_time AS createTime,
            o.print_status AS printStatus,
            o.print_time AS printTime,
            o.write_off_flag AS writeOffFlag,
            c.name AS customerName,
            MAX(g.unit_type) AS unitType,
            MAX(r.provider_name) AS providerName,
            MAX(r.provider_transport_time) AS providerTransportTime,
            COUNT(g.id) AS totalCounts,
            SUM(g.weight) AS totalWeight,
            SUM(g.length * g.width * g.height) AS totalVolume
        FROM tms_store_order o
        LEFT JOIN tms_store_customer c ON c.id = o.store_customer_id
        LEFT JOIN tms_store_order_goods g ON g.main_entrusted_order = o.entrusted_order_number AND g.del_flag = 0
        LEFT JOIN tms_store_provider_relation r ON r.main_entrusted_order = o.entrusted_order_number AND r.del_flag = 0
        <where>
            o.sub_flag = 0
            <if test="storeOrderQueryDTO != null and storeOrderQueryDTO.ids == null">
                <if test="storeOrderQueryDTO.storeCustomerId != null">
                    AND o.store_customer_id = #{storeOrderQueryDTO.storeCustomerId}
                </if>
                <if test="storeOrderQueryDTO.entrustedOrderNumber != null and storeOrderQueryDTO.entrustedOrderNumber != ''">
                    <choose>
                        <when test="storeOrderQueryDTO.entrustedOrderNumber.indexOf(',') > 0">
                            AND FIND_IN_SET(o.entrusted_order_number, #{storeOrderQueryDTO.entrustedOrderNumber})
                        </when>
                        <otherwise>
                            AND o.entrusted_order_number = #{storeOrderQueryDTO.entrustedOrderNumber}
                        </otherwise>
                    </choose>
                </if>
                <if test="storeOrderQueryDTO.externalOrderNumber != null">
                    AND o.external_order_number = #{storeOrderQueryDTO.externalOrderNumber}
                </if>
                <if test="storeOrderQueryDTO.orderTimeStart != null and storeOrderQueryDTO.orderTimeEnd != null">
                    AND o.create_time &gt;= #{storeOrderQueryDTO.orderTimeStart}
                    AND o.create_time &lt;= #{storeOrderQueryDTO.orderTimeEnd}
                </if>
                <if test="storeOrderQueryDTO.orderStatus != null">
                    AND o.order_status = #{storeOrderQueryDTO.orderStatus}
                </if>
                <if test="storeOrderQueryDTO.contacts != null and storeOrderQueryDTO.contacts != ''">
                    AND (o.shipper_name LIKE CONCAT(#{storeOrderQueryDTO.contacts}, '%')
                         OR o.receiver_name LIKE CONCAT(#{storeOrderQueryDTO.contacts}, '%'))
                </if>
                <if test="storeOrderQueryDTO.phone != null and storeOrderQueryDTO.phone != ''">
                    AND (o.shipper_phone LIKE CONCAT(#{storeOrderQueryDTO.phone}, '%')
                         OR o.receiver_phone LIKE CONCAT(#{storeOrderQueryDTO.phone}, '%'))
                </if>
                <if test="storeOrderQueryDTO.shipperOrigin != null and storeOrderQueryDTO.shipperOrigin != ''">
                    AND o.shipper_origin = #{storeOrderQueryDTO.shipperOrigin}
                </if>
                <if test="storeOrderQueryDTO.receiverDest != null and storeOrderQueryDTO.receiverDest != ''">
                    AND o.receiver_dest = #{storeOrderQueryDTO.receiverDest}
                </if>
            </if>
            <if test="storeOrderQueryDTO != null and storeOrderQueryDTO.ids != null">
                AND o.id IN
                <foreach collection="storeOrderQueryDTO.ids" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY o.id
        ORDER BY o.create_time DESC
    </select>

</mapper>
