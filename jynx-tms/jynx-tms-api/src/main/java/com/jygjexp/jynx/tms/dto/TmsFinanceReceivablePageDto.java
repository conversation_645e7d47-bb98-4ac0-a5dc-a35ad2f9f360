package com.jygjexp.jynx.tms.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.WriteConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * @Description
 * @Date 2025/8/5 13:34
 * @Created guqingren
 */
@Data
public class TmsFinanceReceivablePageDto {

    /**
     * 订单id
     */
    @ExcelIgnore
    @Schema(description = "门店id")
    private Long storeId;

    /**
     * 订单数量
     */
    @ExcelProperty("(Number Of Orders)订单数量")
    @Schema(description = "订单数量")
    private Integer numberOfOrders;

    /**
     * 运费总额
     */
    @ExcelProperty("(Price)运费总额")
    @Schema(description = "运费总额")
    private BigDecimal totalFreightAmount;

    /**
     * 订单差价
     */
    @ExcelProperty("(Price Diff)订单差价")
    @Schema(description = "订单差价")
    private BigDecimal priceDifference;

    /**
     * 账单周期
     */
    @ExcelProperty("(Billing Cycle)账单周期")
    @Schema(description = "账单周期")
    private String billingCycle;

    /**
     * 门店名称
     */
    @ExcelProperty("(Store Name)门店名称")
    @Schema(description = "门店名称")
    private String storeName;

    /**
     * 门店类型 0-自营 1-加盟
     */
    @ExcelProperty(value = "(Store Type)门店类型", converter = StoreTypeConverter.class)
    @Schema(description = "门店类型 0-自营 1-加盟")
    private Integer storeType;


    public static class StoreTypeConverter implements Converter<Integer> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return Integer.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public WriteCellData<?> convertToExcelData(WriteConverterContext<Integer> context) throws Exception {
            HashMap<Integer, String> map = new HashMap<>();
            map.put(0, "自营");
            map.put(1, "加盟");
            Integer value = context.getValue();
            if (map.containsKey(value)) {
                return new WriteCellData<>(map.get(value));
            }
            return new WriteCellData<>("");
        }
    }
}
