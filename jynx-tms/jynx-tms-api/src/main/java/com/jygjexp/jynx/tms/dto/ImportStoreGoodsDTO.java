package com.jygjexp.jynx.tms.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class ImportStoreGoodsDTO {
    @Schema(description = "长")
    @ExcelProperty("长-Length(cm)")
    private BigDecimal length;

    @Schema(description = "宽")
    @ExcelProperty("宽-Width(cm)")
    private BigDecimal width;

    @Schema(description = "高")
    @ExcelProperty("高-Height(cm)")
    private BigDecimal height;

    @Schema(description = "重量")
    @ExcelProperty("重量-Weight(kg)")
    private BigDecimal weight;

    @Schema(description = "货物信息")
    @ExcelProperty("货物信息-GoodsInformation")
    private String goodInfo;

    @Schema(description = "备注")
    @ExcelProperty("备注-Remarks")
    private String remark;
}
